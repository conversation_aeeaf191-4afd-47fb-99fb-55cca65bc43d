
import subprocess
import sys

def check_feather_connection():
    """
    Checks for a connected Adafruit Feather RP2040 by listing USB devices.
    """
    try:
        # Execute the lsusb command and capture the output
        result = subprocess.run(['lsusb'], capture_output=True, text=True, check=True)
        output = result.stdout

        # Check for "Adafruit" in the output, which is common for Feather boards
        if "Adafruit" in output:
            print("Adafruit Feather RP2040 is connected.")
            # You can add more specific checks here if you know the USB ID
            # For example, check for the vendor ID 239A
            if "239A:" in output:
                print("Found Adafruit vendor ID.")
            return True
        else:
            print("Adafruit Feather RP2040 not found.")
            return False

    except FileNotFoundError:
        print("Error: 'lsusb' command not found. This script is intended for Linux.")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"Error executing lsusb: {e}")
        sys.exit(1)

if __name__ == "__main__":
    check_feather_connection()
