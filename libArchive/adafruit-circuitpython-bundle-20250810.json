{"adafruit_24lc32": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_24lc32", "pypi_name": "adafruit-circuitpython-24lc32", "repo": "https://github.com/adafruit/adafruit_circuitpython_24lc32", "version": "1.2.3"}, "adafruit_74hc595": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_74hc595", "pypi_name": "adafruit-circuitpython-74hc595", "repo": "https://github.com/adafruit/adafruit_circuitpython_74hc595", "version": "1.4.8"}, "adafruit_acep7in": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_acep7in", "pypi_name": "adafruit-circuitpython-acep7in", "repo": "https://github.com/adafruit/adafruit_circuitpython_acep7in", "version": "1.0.2"}, "adafruit_ad569x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ad569x", "pypi_name": "adafruit-circuitpython-ad569x", "repo": "https://github.com/adafruit/adafruit_circuitpython_ad569x", "version": "2.0.8"}, "adafruit_adg72x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_adg72x", "pypi_name": "adafruit-circuitpython-adg72x", "repo": "https://github.com/adafruit/adafruit_circuitpython_adg72x", "version": "1.0.4"}, "adafruit_ads1x15": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing-extensions"], "package": true, "path": "lib/adafruit_ads1x15", "pypi_name": "adafruit-circuitpython-ads1x15", "repo": "https://github.com/adafruit/adafruit_circuitpython_ads1x15", "version": "2.4.4"}, "adafruit_ads7830": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ads7830", "pypi_name": "adafruit-circuitpython-ads7830", "repo": "https://github.com/adafruit/adafruit_circuitpython_ads7830", "version": "1.0.5"}, "adafruit_adt7410": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_adt7410", "pypi_name": "adafruit-circuitpython-adt7410", "repo": "https://github.com/adafruit/adafruit_circuitpython_adt7410", "version": "2.0.5"}, "adafruit_adxl34x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_adxl34x", "pypi_name": "adafruit-circuitpython-adxl34x", "repo": "https://github.com/adafruit/adafruit_circuitpython_adxl34x", "version": "1.12.17"}, "adafruit_adxl37x": {"dependencies": ["adafruit_adxl34x", "adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_adxl37x", "pypi_name": "adafruit-circuitpython-adxl37x", "repo": "https://github.com/adafruit/adafruit_circuitpython_adxl37x", "version": "1.2.4"}, "adafruit_ags02ma": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ags02ma", "pypi_name": "adafruit-circuitpython-ags02ma", "repo": "https://github.com/adafruit/adafruit_circuitpython_ags02ma", "version": "1.0.12"}, "adafruit_ahtx0": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ahtx0", "pypi_name": "adafruit-circuitpython-ahtx0", "repo": "https://github.com/adafruit/adafruit_circuitpython_ahtx0", "version": "1.0.27"}, "adafruit_airlift": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_airlift", "pypi_name": "adafruit-circuitpython-airlift", "repo": "https://github.com/adafruit/adafruit_circuitpython_airlift", "version": "1.0.18"}, "adafruit_am2320": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_am2320", "pypi_name": "adafruit-circuitpython-am2320", "repo": "https://github.com/adafruit/adafruit_circuitpython_am2320", "version": "1.2.25"}, "adafruit_amg88xx": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_amg88xx", "pypi_name": "adafruit-circuitpython-amg88xx", "repo": "https://github.com/adafruit/adafruit_circuitpython_amg88xx", "version": "1.2.25"}, "adafruit_anchored_group": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_anchored_group", "pypi_name": "adafruit-circuitpython-anchored-group", "repo": "https://github.com/adafruit/adafruit_circuitpython_anchored_group", "version": "1.0.0"}, "adafruit_anchored_tilegrid": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_anchored_tilegrid", "pypi_name": "adafruit-circuitpython-anchored-tilegrid", "repo": "https://github.com/adafruit/adafruit_circuitpython_anchored_tilegrid", "version": "1.0.1"}, "adafruit_apds9960": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_apds9960", "pypi_name": "adafruit-circuitpython-apds9960", "repo": "https://github.com/adafruit/adafruit_circuitpython_apds9960", "version": "3.1.16"}, "adafruit_argv_file": {"dependencies": ["adafruit_pathlib"], "external_dependencies": [], "package": false, "path": "lib/adafruit_argv_file", "pypi_name": "adafruit-circuitpython-argv-file", "repo": "https://github.com/adafruit/adafruit_circuitpython_argv_file", "version": "1.0.0"}, "adafruit_as5600": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_as5600", "pypi_name": "adafruit-circuitpython-as5600", "repo": "https://github.com/adafruit/adafruit_circuitpython_as5600", "version": "1.0.0"}, "adafruit_as726x": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_as726x", "pypi_name": "adafruit-circuitpython-as726x", "repo": "https://github.com/adafruit/adafruit_circuitpython_as726x", "version": "2.0.22"}, "adafruit_as7341": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_as7341", "pypi_name": "adafruit-circuitpython-as7341", "repo": "https://github.com/adafruit/adafruit_circuitpython_as7341", "version": "1.2.23"}, "adafruit_atecc": {"dependencies": ["adafruit_binascii", "adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_atecc", "pypi_name": "adafruit-circuitpython-atecc", "repo": "https://github.com/adafruit/adafruit_circuitpython_atecc", "version": "1.2.24"}, "adafruit_avrprog": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_avrprog", "pypi_name": "adafruit-circuitpython-avrprog", "repo": "https://github.com/adafruit/adafruit_circuitpython_avrprog", "version": "1.5.5"}, "adafruit_aw9523": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_aw9523", "pypi_name": "adafruit-circuitpython-aw9523", "repo": "https://github.com/adafruit/adafruit_circuitpython_aw9523", "version": "1.1.13"}, "adafruit_aws_iot": {"dependencies": ["adafruit_minimqtt"], "external_dependencies": [], "package": false, "path": "lib/adafruit_aws_iot", "pypi_name": "adafruit-circuitpython-aws-iot", "repo": "https://github.com/adafruit/adafruit_circuitpython_aws_iot", "version": "3.0.2"}, "adafruit_azureiot": {"dependencies": ["adafruit_binascii", "adafruit_logging", "adafruit_minimqtt"], "external_dependencies": [], "package": true, "path": "lib/adafruit_azureiot", "pypi_name": "adafruit-circuitpython-azureiot", "repo": "https://github.com/adafruit/adafruit_circuitpython_azureiot", "version": "3.0.2"}, "adafruit_bd3491fs": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bd3491fs", "pypi_name": "adafruit-circuitpython-bd3491fs", "repo": "https://github.com/adafruit/adafruit_circuitpython_bd3491fs", "version": "1.1.21"}, "adafruit_bh1750": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bh1750", "pypi_name": "adafruit-circuitpython-bh1750", "repo": "https://github.com/adafruit/adafruit_circuitpython_bh1750", "version": "1.1.16"}, "adafruit_binascii": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_binascii", "pypi_name": "adafruit-circuitpython-binascii", "repo": "https://github.com/adafruit/adafruit_circuitpython_binascii", "version": "2.0.7"}, "adafruit_bitbangio": {"dependencies": [], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_bitbangio", "pypi_name": "adafruit-circuitpython-bitbangio", "repo": "https://github.com/adafruit/adafruit_circuitpython_bitbangio", "version": "1.3.19"}, "adafruit_bitmap_font": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_bitmap_font", "pypi_name": "adafruit-circuitpython-bitmap-font", "repo": "https://github.com/adafruit/adafruit_circuitpython_bitmap_font", "version": "2.3.1"}, "adafruit_bitmapsaver": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_bitmapsaver", "pypi_name": "adafruit-circuitpython-bitmapsaver", "repo": "https://github.com/adafruit/adafruit_circuitpython_bitmapsaver", "version": "1.3.7"}, "adafruit_ble": {"dependencies": [], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": true, "path": "lib/adafruit_ble", "pypi_name": "adafruit-circuitpython-ble", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble", "version": "10.0.14"}, "adafruit_ble_adafruit": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ble_adafruit", "pypi_name": "adafruit-circuitpython-ble-adafruit", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_adafruit", "version": "1.4.13"}, "adafruit_ble_apple_media": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_apple_media", "pypi_name": "adafruit-circuitpython-ble-apple-media", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_apple_media", "version": "0.9.20"}, "adafruit_ble_apple_notification_center": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_apple_notification_center", "pypi_name": "adafruit-circuitpython-ble-apple-notification-center", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_apple_notification_center", "version": "0.10.13"}, "adafruit_ble_beacon": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_beacon", "pypi_name": "adafruit-circuitpython-ble-beacon", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_beacon", "version": "1.0.10"}, "adafruit_ble_berrymed_pulse_oximeter": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ble_berrymed_pulse_oximeter", "pypi_name": "adafruit-circuitpython-ble-berrymed-pulse-oximeter", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_berrymed_pulse_oximeter", "version": "2.0.21"}, "adafruit_ble_broadcastnet": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_broadcastnet", "pypi_name": "adafruit-circuitpython-ble-broadcastnet", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_broadcastnet", "version": "0.12.15"}, "adafruit_ble_cycling_speed_and_cadence": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_cycling_speed_and_cadence", "pypi_name": "adafruit-circuitpython-ble-cycling-speed-and-cadence", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_cycling_speed_and_cadence", "version": "1.1.19"}, "adafruit_ble_eddystone": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ble_eddystone", "pypi_name": "adafruit-circuitpython-ble-eddystone", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_eddystone", "version": "1.10.22"}, "adafruit_ble_file_transfer": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_file_transfer", "pypi_name": "adafruit-circuitpython-ble-file-transfer", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_file_transfer", "version": "4.0.11"}, "adafruit_ble_heart_rate": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_heart_rate", "pypi_name": "adafruit-circuitpython-ble-heart-rate", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_heart_rate", "version": "1.1.22"}, "adafruit_ble_ibbq": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_ibbq", "pypi_name": "adafruit-circuitpython-ble-ibbq", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_ibbq", "version": "1.2.20"}, "adafruit_ble_lywsd03mmc": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_lywsd03mmc", "pypi_name": "adafruit-circuitpython-ble-lywsd03mmc", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_lywsd03mmc", "version": "1.0.20"}, "adafruit_ble_magic_light": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_magic_light", "pypi_name": "adafruit-circuitpython-ble-magic-light", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_magic_light", "version": "0.9.24"}, "adafruit_ble_midi": {"dependencies": ["adafruit_ble"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_ble_midi", "pypi_name": "adafruit-circuitpython-ble-midi", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_midi", "version": "1.0.19"}, "adafruit_ble_radio": {"dependencies": ["adafruit_ble"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ble_radio", "pypi_name": "adafruit-circuitpython-ble-radio", "repo": "https://github.com/adafruit/adafruit_circuitpython_ble_radio", "version": "0.5.14"}, "adafruit_bluefruit_connect": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_bluefruit_connect", "pypi_name": "adafruit-circuitpython-bluefruitconnect", "repo": "https://github.com/adafruit/adafruit_circuitpython_bluefruitconnect", "version": "1.2.18"}, "adafruit_bluefruitspi": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bluefruitspi", "pypi_name": "adafruit-circuitpython-bluefruitspi", "repo": "https://github.com/adafruit/adafruit_circuitpython_bluefruitspi", "version": "1.1.23"}, "adafruit_bme280": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_bme280", "pypi_name": "adafruit-circuitpython-bme280", "repo": "https://github.com/adafruit/adafruit_circuitpython_bme280", "version": "2.6.29"}, "adafruit_bme680": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bme680", "pypi_name": "adafruit-circuitpython-bme680", "repo": "https://github.com/adafruit/adafruit_circuitpython_bme680", "version": "3.7.13"}, "adafruit_bmp280": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bmp280", "pypi_name": "adafruit-circuitpython-bmp280", "repo": "https://github.com/adafruit/adafruit_circuitpython_bmp280", "version": "3.3.8"}, "adafruit_bmp3xx": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bmp3xx", "pypi_name": "adafruit-circuitpython-bmp3xx", "repo": "https://github.com/adafruit/adafruit_circuitpython_bmp3xx", "version": "1.3.24"}, "adafruit_bno055": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_bno055", "pypi_name": "adafruit-circuitpython-bno055", "repo": "https://github.com/adafruit/adafruit_circuitpython_bno055", "version": "5.4.20"}, "adafruit_bno08x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_bno08x", "pypi_name": "adafruit-circuitpython-bno08x", "repo": "https://github.com/adafruit/adafruit_circuitpython_bno08x", "version": "1.2.10"}, "adafruit_bno08x_rvc": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_bno08x_rvc", "pypi_name": "adafruit-circuitpython-bno08x-rvc", "repo": "https://github.com/adafruit/adafruit_circuitpython_bno08x_rvc", "version": "1.0.21"}, "adafruit_boardtest": {"dependencies": ["adafruit_bus_device", "adafruit_sdcard"], "external_dependencies": [], "package": true, "path": "lib/adafruit_boardtest", "pypi_name": "adafruit-circuitpython-boardtest", "repo": "https://github.com/adafruit/adafruit_circuitpython_boardtest", "version": "1.2.24"}, "adafruit_bus_device": {"dependencies": [], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_bus_device", "pypi_name": "adafruit-circuitpython-busdevice", "repo": "https://github.com/adafruit/adafruit_circuitpython_busdevice", "version": "5.2.13"}, "adafruit_button": {"dependencies": ["adafruit_bitmap_font", "adafruit_display_shapes", "adafruit_display_text"], "external_dependencies": [], "package": true, "path": "lib/adafruit_button", "pypi_name": "adafruit-circuitpython-display-button", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_button", "version": "1.11.5"}, "adafruit_cap1188": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_cap1188", "pypi_name": "adafruit-circuitpython-cap1188", "repo": "https://github.com/adafruit/adafruit_circuitpython_cap1188", "version": "1.3.16"}, "adafruit_ccs811": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ccs811", "pypi_name": "adafruit-circuitpython-ccs811", "repo": "https://github.com/adafruit/adafruit_circuitpython_ccs811", "version": "1.3.21"}, "adafruit_ch9328": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_ch9328", "pypi_name": "adafruit-circuitpython-ch9328", "repo": "https://github.com/adafruit/adafruit_circuitpython_ch9328", "version": "1.0.5"}, "adafruit_character_lcd": {"dependencies": ["adafruit_74hc595", "adafruit_bus_device", "adafruit_mcp230xx"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_character_lcd", "pypi_name": "adafruit-circuitpython-charlcd", "repo": "https://github.com/adafruit/adafruit_circuitpython_charlcd", "version": "3.5.3"}, "adafruit_circuitplayground": {"dependencies": ["adafruit_lis3dh", "adafruit_thermistor", "neopixel"], "external_dependencies": ["typing-extensions"], "package": true, "path": "lib/adafruit_circuitplayground", "pypi_name": "adafruit-circuitpython-circuitplayground", "repo": "https://github.com/adafruit/adafruit_circuitpython_circuitplayground", "version": "5.3.9"}, "adafruit_clue": {"dependencies": ["adafruit_apds9960", "adafruit_bmp280", "adafruit_bus_device", "adafruit_display_text", "adafruit_lis3mdl", "adafruit_lsm6ds", "adafruit_register", "adafruit_sht31d", "neopixel"], "external_dependencies": [], "package": false, "path": "lib/adafruit_clue", "pypi_name": "adafruit-circuitpython-clue", "repo": "https://github.com/adafruit/adafruit_circuitpython_clue", "version": "3.2.4"}, "adafruit_color_terminal": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_color_terminal", "pypi_name": "adafruit-circuitpython-color-terminal", "repo": "https://github.com/adafruit/adafruit_circuitpython_color_terminal", "version": "0.2.0"}, "adafruit_connection_manager": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_connection_manager", "pypi_name": "adafruit-circuitpython-connectionmanager", "repo": "https://github.com/adafruit/adafruit_circuitpython_connectionmanager", "version": "3.1.5"}, "adafruit_crickit": {"dependencies": ["adafruit_motor", "adafruit_seesaw"], "external_dependencies": [], "package": false, "path": "lib/adafruit_crickit", "pypi_name": "adafruit-circuitpython-crickit", "repo": "https://github.com/adafruit/adafruit_circuitpython_crickit", "version": "2.3.21"}, "adafruit_cst8xx": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_cst8xx", "pypi_name": "adafruit-circuitpython-cst8xx", "repo": "https://github.com/adafruit/adafruit_circuitpython_cst8xx", "version": "1.0.5"}, "adafruit_cursorcontrol": {"dependencies": ["adafruit_debouncer"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_cursorcontrol", "pypi_name": "adafruit-circuitpython-cursorcontrol", "repo": "https://github.com/adafruit/adafruit_circuitpython_cursorcontrol", "version": "2.9.4"}, "adafruit_dacx578": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_dacx578", "pypi_name": "adafruit-circuitpython-dacx578", "repo": "https://github.com/adafruit/adafruit_circuitpython_dacx578", "version": "1.0.0"}, "adafruit_dang": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_dang", "pypi_name": "adafruit-circuitpython-dang", "repo": "https://github.com/adafruit/adafruit_circuitpython_dang", "version": "1.0.0"}, "adafruit_dash_display": {"dependencies": ["adafruit_display_shapes", "adafruit_display_text", "adafruit_io"], "external_dependencies": [], "package": false, "path": "lib/adafruit_dash_display", "pypi_name": "adafruit-circuitpython-dash-display", "repo": "https://github.com/adafruit/adafruit_circuitpython_dash_display", "version": "3.0.4"}, "adafruit_datetime": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_datetime", "pypi_name": "adafruit-circuitpython-datetime", "repo": "https://github.com/adafruit/adafruit_circuitpython_datetime", "version": "1.4.3"}, "adafruit_debouncer": {"dependencies": ["adafruit_ticks"], "external_dependencies": [], "package": false, "path": "lib/adafruit_debouncer", "pypi_name": "adafruit-circuitpython-debouncer", "repo": "https://github.com/adafruit/adafruit_circuitpython_debouncer", "version": "2.0.12"}, "adafruit_debug_i2c": {"dependencies": [], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_debug_i2c", "pypi_name": "adafruit-circuitpython-debug-i2c", "repo": "https://github.com/adafruit/adafruit_circuitpython_debug_i2c", "version": "1.2.21"}, "adafruit_dht": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_dht", "pypi_name": "adafruit-circuitpython-dht", "repo": "https://github.com/adafruit/adafruit_circuitpython_dht", "version": "4.0.9"}, "adafruit_display_analogclock": {"dependencies": ["adafruit_anchored_tilegrid", "adafruit_display_text", "adafruit_imageload"], "external_dependencies": [], "package": false, "path": "lib/adafruit_display_analogclock", "pypi_name": "adafruit-circuitpython-display-analogclock", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_analogclock", "version": "1.0.0"}, "adafruit_display_emoji_text": {"dependencies": ["adafruit_displayio_layout"], "external_dependencies": [], "package": false, "path": "lib/adafruit_display_emoji_text", "pypi_name": "adafruit-circuitpython-display-emoji-text", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_emoji_text", "version": "1.0.1"}, "adafruit_display_notification": {"dependencies": ["adafruit_display_text"], "external_dependencies": [], "package": true, "path": "lib/adafruit_display_notification", "pypi_name": "adafruit-circuitpython-display-notification", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_notification", "version": "1.0.5"}, "adafruit_display_shapes": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_display_shapes", "pypi_name": "adafruit-circuitpython-display-shapes", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_shapes", "version": "2.10.4"}, "adafruit_display_text": {"dependencies": ["adafruit_bitmap_font", "adafruit_ticks"], "external_dependencies": [], "package": true, "path": "lib/adafruit_display_text", "pypi_name": "adafruit-circuitpython-display-text", "repo": "https://github.com/adafruit/adafruit_circuitpython_display_text", "version": "3.3.3"}, "adafruit_displayio_flipclock": {"dependencies": ["adafruit_bus_device", "adafruit_displayio_layout", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_displayio_flipclock", "pypi_name": "adafruit-circuitpython-displayio-flipclock", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_flipclock", "version": "1.1.7"}, "adafruit_displayio_layout": {"dependencies": ["adafruit_bitmap_font", "adafruit_display_shapes", "adafruit_display_text", "adafruit_imageload"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_displayio_layout", "pypi_name": "adafruit-circuitpython-displayio-layout", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_layout", "version": "3.1.0"}, "adafruit_displayio_sh1106": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_displayio_sh1106", "pypi_name": "adafruit-circuitpython-displayio-sh1106", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_sh1106", "version": "2.0.1"}, "adafruit_displayio_sh1107": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_displayio_sh1107", "pypi_name": "adafruit-circuitpython-displayio-sh1107", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_sh1107", "version": "2.0.3"}, "adafruit_displayio_ssd1305": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_displayio_ssd1305", "pypi_name": "adafruit-circuitpython-displayio-ssd1305", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_ssd1305", "version": "2.0.1"}, "adafruit_displayio_ssd1306": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_displayio_ssd1306", "pypi_name": "adafruit-circuitpython-displayio-ssd1306", "repo": "https://github.com/adafruit/adafruit_circuitpython_displayio_ssd1306", "version": "3.0.3"}, "adafruit_dotstar": {"dependencies": ["adafruit_pixelbuf"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_dotstar", "pypi_name": "adafruit-circuitpython-dotstar", "repo": "https://github.com/adafruit/adafruit_circuitpython_dotstar", "version": "2.2.17"}, "adafruit_dps310": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_dps310", "pypi_name": "adafruit-circuitpython-dps310", "repo": "https://github.com/adafruit/adafruit_circuitpython_dps310", "version": "2.1.19"}, "adafruit_drv2605": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_drv2605", "pypi_name": "adafruit-circuitpython-drv2605", "repo": "https://github.com/adafruit/adafruit_circuitpython_drv2605", "version": "1.3.7"}, "adafruit_ds1307": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ds1307", "pypi_name": "adafruit-circuitpython-ds1307", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds1307", "version": "2.1.24"}, "adafruit_ds1841": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ds1841", "pypi_name": "adafruit-circuitpython-ds1841", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds1841", "version": "1.0.22"}, "adafruit_ds18x20": {"dependencies": ["adafruit_onewire"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_ds18x20", "pypi_name": "adafruit-circuitpython-ds18x20", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds18x20", "version": "1.4.6"}, "adafruit_ds2413": {"dependencies": ["adafruit_bus_device", "adafruit_onewire"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_ds2413", "pypi_name": "adafruit-circuitpython-ds2413", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds2413", "version": "1.2.21"}, "adafruit_ds248x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ds248x", "pypi_name": "adafruit-circuitpython-ds248x", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds248x", "version": "1.2.4"}, "adafruit_ds3231": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ds3231", "pypi_name": "adafruit-circuitpython-ds3231", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds3231", "version": "2.4.25"}, "adafruit_ds3502": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ds3502", "pypi_name": "adafruit-circuitpython-ds3502", "repo": "https://github.com/adafruit/adafruit_circuitpython_ds3502", "version": "1.1.24"}, "adafruit_ducky": {"dependencies": ["adafruit_hid"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ducky", "pypi_name": "adafruit-circuitpython-ducky", "repo": "https://github.com/adafruit/adafruit_circuitpython_ducky", "version": "1.5.3"}, "adafruit_dymoscale": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_dymoscale", "pypi_name": "adafruit-circuitpython-dymoscale", "repo": "https://github.com/adafruit/adafruit_circuitpython_dymoscale", "version": "2.0.11"}, "adafruit_ek79686": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ek79686", "pypi_name": "adafruit-circuitpython-ek79686", "repo": "https://github.com/adafruit/adafruit_circuitpython_ek79686", "version": "2.0.3"}, "adafruit_emc2101": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_emc2101", "pypi_name": "adafruit-circuitpython-emc2101", "repo": "https://github.com/adafruit/adafruit_circuitpython_emc2101", "version": "1.2.11"}, "adafruit_ens160": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_ens160", "pypi_name": "adafruit-circuitpython-ens160", "repo": "https://github.com/adafruit/adafruit_circuitpython_ens160", "version": "1.0.12"}, "adafruit_epd": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": ["typing-extensions"], "package": true, "path": "lib/adafruit_epd", "pypi_name": "adafruit-circuitpython-epd", "repo": "https://github.com/adafruit/adafruit_circuitpython_epd", "version": "2.14.0"}, "adafruit_esp32s2tft": {"dependencies": ["adafruit_minimqtt", "adafruit_portalbase", "adafruit_requests", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_esp32s2tft", "pypi_name": "adafruit-circuitpython-esp32s2tft", "repo": "https://github.com/adafruit/adafruit_circuitpython_esp32s2tft", "version": "2.0.3"}, "adafruit_esp32spi": {"dependencies": ["adafruit_bus_device", "adafruit_connection_manager", "adafruit_requests"], "external_dependencies": [], "package": true, "path": "lib/adafruit_esp32spi", "pypi_name": "adafruit-circuitpython-esp32spi", "repo": "https://github.com/adafruit/adafruit_circuitpython_esp32spi", "version": "9.0.3"}, "adafruit_espatcontrol": {"dependencies": ["adafruit_connection_manager", "adafruit_requests"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_espatcontrol", "pypi_name": "adafruit-circuitpython-esp-atcontrol", "repo": "https://github.com/adafruit/adafruit_circuitpython_esp_atcontrol", "version": "0.11.4"}, "adafruit_fakerequests": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_fakerequests", "pypi_name": "adafruit-circuitpython-fakerequests", "repo": "https://github.com/adafruit/adafruit_circuitpython_fakerequests", "version": "1.1.0"}, "adafruit_fancyled": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_fancyled", "pypi_name": "adafruit-circuitpython-fancyled", "repo": "https://github.com/adafruit/adafruit_circuitpython_fancyled", "version": "1.4.23"}, "adafruit_featherwing": {"dependencies": ["adafruit_adt7410", "adafruit_adxl34x", "adafruit_bus_device", "adafruit_dotstar", "adafruit_ds3231", "adafruit_focaltouch", "adafruit_gps", "adafruit_ht16k33", "adafruit_hx8357", "adafruit_ili9341", "adafruit_ina219", "adafruit_register", "adafruit_seesaw", "adafruit_st7735r", "adafruit_stmpe610", "adafruit_tsc2007", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_featherwing", "pypi_name": "adafruit-circuitpython-featherwing", "repo": "https://github.com/adafruit/adafruit_circuitpython_featherwing", "version": "1.15.7"}, "adafruit_fingerprint": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_fingerprint", "pypi_name": "adafruit-circuitpython-fingerprint", "repo": "https://github.com/adafruit/adafruit_circuitpython_fingerprint", "version": "2.2.22"}, "adafruit_floppy": {"dependencies": ["adafruit_ticks"], "external_dependencies": [], "package": false, "path": "lib/adafruit_floppy", "pypi_name": "adafruit-circuitpython-floppy", "repo": "https://github.com/adafruit/adafruit_circuitpython_floppy", "version": "3.0.3"}, "adafruit_focaltouch": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_focaltouch", "pypi_name": "adafruit-circuitpython-focaltouch", "repo": "https://github.com/adafruit/adafruit_circuitpython_focaltouch", "version": "1.5.6"}, "adafruit_fona": {"dependencies": ["adafruit_bus_device", "simpleio"], "external_dependencies": [], "package": true, "path": "lib/adafruit_fona", "pypi_name": "adafruit-circuitpython-fona", "repo": "https://github.com/adafruit/adafruit_circuitpython_fona", "version": "3.0.9"}, "adafruit_fram": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_fram", "pypi_name": "adafruit-circuitpython-fram", "repo": "https://github.com/adafruit/adafruit_circuitpython_fram", "version": "1.3.26"}, "adafruit_framebuf": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_framebuf", "pypi_name": "adafruit-circuitpython-framebuf", "repo": "https://github.com/adafruit/adafruit_circuitpython_framebuf", "version": "1.6.9"}, "adafruit_fruitjam": {"dependencies": ["adafruit_bitmap_font", "adafruit_bus_device", "adafruit_display_text", "adafruit_esp32spi", "adafruit_portalbase", "adafruit_requests", "adafruit_sdcard", "adafruit_tlv320", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_fruitjam", "pypi_name": "adafruit-circuitpython-fruitjam", "repo": "https://github.com/adafruit/adafruit_circuitpython_fruitjam", "version": "0.5.0"}, "adafruit_ft5336": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ft5336", "pypi_name": "adafruit-circuitpython-ft5336", "repo": "https://github.com/adafruit/adafruit_circuitpython_ft5336", "version": "1.1.5"}, "adafruit_funhouse": {"dependencies": ["adafruit_ahtx0", "adafruit_dotstar", "adafruit_dps310", "adafruit_minimqtt", "adafruit_portalbase", "adafruit_requests", "simpleio"], "external_dependencies": [], "package": true, "path": "lib/adafruit_funhouse", "pypi_name": "adafruit-circuitpython-funhouse", "repo": "https://github.com/adafruit/adafruit_circuitpython_funhouse", "version": "2.4.1"}, "adafruit_fxas21002c": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_fxas21002c", "pypi_name": "adafruit-circuitpython-fxas21002c", "repo": "https://github.com/adafruit/adafruit_circuitpython_fxas21002c", "version": "3.0.7"}, "adafruit_fxos8700": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_fxos8700", "pypi_name": "adafruit-circuitpython-fxos8700", "repo": "https://github.com/adafruit/adafruit_circuitpython_fxos8700", "version": "3.0.6"}, "adafruit_gc9a01a": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_gc9a01a", "pypi_name": "adafruit-circuitpython-gc9a01a", "repo": "https://github.com/adafruit/adafruit_circuitpython_gc9a01a", "version": "1.0.0"}, "adafruit_gc_iot_core": {"dependencies": ["adafruit_esp32spi", "adafruit_hashlib", "adafruit_jwt", "adafruit_logging", "adafruit_minimqtt", "adafruit_rsa"], "external_dependencies": [], "package": false, "path": "lib/adafruit_gc_iot_core", "pypi_name": "adafruit-circuitpython-gc-iot-core", "repo": "https://github.com/adafruit/adafruit_circuitpython_gc_iot_core", "version": "4.0.2"}, "adafruit_gfx": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_gfx", "pypi_name": "adafruit-circuitpython-gfx", "repo": "https://github.com/adafruit/adafruit_circuitpython_gfx", "version": "1.1.25"}, "adafruit_gizmo": {"dependencies": ["adafruit_bus_device", "adafruit_il0373", "adafruit_ssd1681", "adafruit_st7789"], "external_dependencies": [], "package": true, "path": "lib/adafruit_gizmo", "pypi_name": "adafruit-circuitpython-gizmo", "repo": "https://github.com/adafruit/adafruit_circuitpython_gizmo", "version": "1.3.24"}, "adafruit_gps": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": false, "path": "lib/adafruit_gps", "pypi_name": "adafruit-circuitpython-gps", "repo": "https://github.com/adafruit/adafruit_circuitpython_gps", "version": "3.11.4"}, "adafruit_guvx_i2c": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_guvx_i2c", "pypi_name": "adafruit-circuitpython-guvx-i2c", "repo": "https://github.com/adafruit/adafruit_circuitpython_guvx_i2c", "version": "1.0.11"}, "adafruit_hashlib": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_hashlib", "pypi_name": "adafruit-circuitpython-hashlib", "repo": "https://github.com/adafruit/adafruit_circuitpython_hashlib", "version": "1.4.19"}, "adafruit_hcsr04": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_hcsr04", "pypi_name": "adafruit-circuitpython-hcsr04", "repo": "https://github.com/adafruit/adafruit_circuitpython_hcsr04", "version": "0.4.22"}, "adafruit_hdc302x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_hdc302x", "pypi_name": "adafruit-circuitpython-hdc302x", "repo": "https://github.com/adafruit/adafruit_circuitpython_hdc302x", "version": "1.0.6"}, "adafruit_hid": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_hid", "pypi_name": "adafruit-circuitpython-hid", "repo": "https://github.com/adafruit/adafruit_circuitpython_hid", "version": "6.1.7"}, "adafruit_ht16k33": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_ht16k33", "pypi_name": "adafruit-circuitpython-ht16k33", "repo": "https://github.com/adafruit/adafruit_circuitpython_ht16k33", "version": "4.6.14"}, "adafruit_hts221": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_hts221", "pypi_name": "adafruit-circuitpython-hts221", "repo": "https://github.com/adafruit/adafruit_circuitpython_hts221", "version": "1.1.21"}, "adafruit_httpserver": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_httpserver", "pypi_name": "adafruit-circuitpython-httpserver", "repo": "https://github.com/adafruit/adafruit_circuitpython_httpserver", "version": "4.7.0"}, "adafruit_htu21d": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_htu21d", "pypi_name": "adafruit-circuitpython-htu21d", "repo": "https://github.com/adafruit/adafruit_circuitpython_htu21d", "version": "0.11.19"}, "adafruit_htu31d": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_htu31d", "pypi_name": "adafruit-circuitpython-htu31d", "repo": "https://github.com/adafruit/adafruit_circuitpython_htu31d", "version": "1.1.19"}, "adafruit_hue": {"dependencies": ["adafruit_esp32spi", "simpleio"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_hue", "pypi_name": "adafruit-circuitpython-hue", "repo": "https://github.com/adafruit/adafruit_circuitpython_hue", "version": "2.0.3"}, "adafruit_husb238": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_husb238", "pypi_name": "adafruit-circuitpython-husb238", "repo": "https://github.com/adafruit/adafruit_circuitpython_husb238", "version": "1.0.5"}, "adafruit_hx711": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_hx711", "pypi_name": "adafruit-circuitpython-hx711", "repo": "https://github.com/adafruit/adafruit_circuitpython_hx711", "version": "1.0.7"}, "adafruit_hx8357": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_hx8357", "pypi_name": "adafruit-circuitpython-hx8357", "repo": "https://github.com/adafruit/adafruit_circuitpython_hx8357", "version": "2.0.1"}, "adafruit_icm20x": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_icm20x", "pypi_name": "adafruit-circuitpython-icm20x", "repo": "https://github.com/adafruit/adafruit_circuitpython_icm20x", "version": "2.1.7"}, "adafruit_il0373": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_il0373", "pypi_name": "adafruit-circuitpython-il0373", "repo": "https://github.com/adafruit/adafruit_circuitpython_il0373", "version": "2.0.2"}, "adafruit_il0398": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_il0398", "pypi_name": "adafruit-circuitpython-il0398", "repo": "https://github.com/adafruit/adafruit_circuitpython_il0398", "version": "2.0.2"}, "adafruit_il91874": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_il91874", "pypi_name": "adafruit-circuitpython-il91874", "repo": "https://github.com/adafruit/adafruit_circuitpython_il91874", "version": "2.0.2"}, "adafruit_ili9341": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ili9341", "pypi_name": "adafruit-circuitpython-ili9341", "repo": "https://github.com/adafruit/adafruit_circuitpython_ili9341", "version": "2.0.1"}, "adafruit_imageload": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_imageload", "pypi_name": "adafruit-circuitpython-imageload", "repo": "https://github.com/adafruit/adafruit_circuitpython_imageload", "version": "1.24.4"}, "adafruit_ina219": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ina219", "pypi_name": "adafruit-circuitpython-ina219", "repo": "https://github.com/adafruit/adafruit_circuitpython_ina219", "version": "3.4.29"}, "adafruit_ina228": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ina228", "pypi_name": "adafruit-circuitpython-ina228", "repo": "https://github.com/adafruit/adafruit_circuitpython_ina228", "version": "2.0.1"}, "adafruit_ina23x": {"dependencies": ["adafruit_bus_device", "adafruit_ina228", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ina23x", "pypi_name": "adafruit-circuitpython-ina23x", "repo": "https://github.com/adafruit/adafruit_circuitpython_ina23x", "version": "1.0.0"}, "adafruit_ina260": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ina260", "pypi_name": "adafruit-circuitpython-ina260", "repo": "https://github.com/adafruit/adafruit_circuitpython_ina260", "version": "1.3.19"}, "adafruit_ina3221": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ina3221", "pypi_name": "adafruit-circuitpython-ina3221", "repo": "https://github.com/adafruit/adafruit_circuitpython_ina3221", "version": "1.2.0"}, "adafruit_io": {"dependencies": ["adafruit_minimqtt"], "external_dependencies": [], "package": true, "path": "lib/adafruit_io", "pypi_name": "adafruit-circuitpython-adafruitio", "repo": "https://github.com/adafruit/adafruit_circuitpython_adafruitio", "version": "6.0.3"}, "adafruit_irremote": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_irremote", "pypi_name": "adafruit-circuitpython-irremote", "repo": "https://github.com/adafruit/adafruit_circuitpython_irremote", "version": "5.0.5"}, "adafruit_is31fl3731": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_is31fl3731", "pypi_name": "adafruit-circuitpython-is31fl3731", "repo": "https://github.com/adafruit/adafruit_circuitpython_is31fl3731", "version": "3.4.5"}, "adafruit_is31fl3741": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf", "adafruit_register"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_is31fl3741", "pypi_name": "adafruit-circuitpython-is31fl3741", "repo": "https://github.com/adafruit/adafruit_circuitpython_is31fl3741", "version": "1.5.5"}, "adafruit_itertools": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_itertools", "pypi_name": "adafruit-circuitpython-itertools", "repo": "https://github.com/adafruit/adafruit_circuitpython_itertools", "version": "2.1.5"}, "adafruit_jd79661": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_jd79661", "pypi_name": "adafruit-circuitpython-jd79661", "repo": "https://github.com/adafruit/adafruit_circuitpython_jd79661", "version": "1.0.0"}, "adafruit_json_stream": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_json_stream", "pypi_name": "adafruit-circuitpython-json-stream", "repo": "https://github.com/adafruit/adafruit_circuitpython_json_stream", "version": "0.9.2"}, "adafruit_jwt": {"dependencies": ["adafruit_binascii", "adafruit_rsa"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_jwt", "pypi_name": "adafruit-circuitpython-jwt", "repo": "https://github.com/adafruit/adafruit_circuitpython_jwt", "version": "1.2.22"}, "adafruit_l3gd20": {"dependencies": ["adafruit_register"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_l3gd20", "pypi_name": "adafruit-circuitpython-l3gd20", "repo": "https://github.com/adafruit/adafruit_circuitpython_l3gd20", "version": "2.3.24"}, "adafruit_lc709203f": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": false, "path": "lib/adafruit_lc709203f", "pypi_name": "adafruit-circuitpython-lc709203f", "repo": "https://github.com/adafruit/adafruit_circuitpython_lc709203f", "version": "2.3.7"}, "adafruit_led_animation": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_led_animation", "pypi_name": "adafruit-circuitpython-led-animation", "repo": "https://github.com/adafruit/adafruit_circuitpython_led_animation", "version": "2.12.3"}, "adafruit_lidarlite": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lidarlite", "pypi_name": "adafruit-circuitpython-lidarlite", "repo": "https://github.com/adafruit/adafruit_circuitpython_lidarlite", "version": "1.3.11"}, "adafruit_lifx": {"dependencies": ["adafruit_requests"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_lifx", "pypi_name": "adafruit-circuitpython-lifx", "repo": "https://github.com/adafruit/adafruit_circuitpython_lifx", "version": "3.0.3"}, "adafruit_lis2mdl": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_lis2mdl", "pypi_name": "adafruit-circuitpython-lis2mdl", "repo": "https://github.com/adafruit/adafruit_circuitpython_lis2mdl", "version": "2.1.27"}, "adafruit_lis331": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lis331", "pypi_name": "adafruit-circuitpython-lis331", "repo": "https://github.com/adafruit/adafruit_circuitpython_lis331", "version": "1.0.22"}, "adafruit_lis3dh": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lis3dh", "pypi_name": "adafruit-circuitpython-lis3dh", "repo": "https://github.com/adafruit/adafruit_circuitpython_lis3dh", "version": "5.2.6"}, "adafruit_lis3mdl": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lis3mdl", "pypi_name": "adafruit-circuitpython-lis3mdl", "repo": "https://github.com/adafruit/adafruit_circuitpython_lis3mdl", "version": "1.2.4"}, "adafruit_logging": {"dependencies": [], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_logging", "pypi_name": "adafruit-circuitpython-logging", "repo": "https://github.com/adafruit/adafruit_circuitpython_logging", "version": "5.5.5"}, "adafruit_lps28": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lps28", "pypi_name": "adafruit-circuitpython-lps28", "repo": "https://github.com/adafruit/adafruit_circuitpython_lps28", "version": "1.0.0"}, "adafruit_lps2x": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lps2x", "pypi_name": "adafruit-circuitpython-lps2x", "repo": "https://github.com/adafruit/adafruit_circuitpython_lps2x", "version": "3.0.12"}, "adafruit_lps35hw": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lps35hw", "pypi_name": "adafruit-circuitpython-lps35hw", "repo": "https://github.com/adafruit/adafruit_circuitpython_lps35hw", "version": "1.2.23"}, "adafruit_lsm303_accel": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_lsm303_accel", "pypi_name": "adafruit-circuitpython-lsm303-accel", "repo": "https://github.com/adafruit/adafruit_circuitpython_lsm303_accel", "version": "1.1.25"}, "adafruit_lsm303dlh_mag": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lsm303dlh_mag", "pypi_name": "adafruit-circuitpython-lsm303dlh-mag", "repo": "https://github.com/adafruit/adafruit_circuitpython_lsm303dlh_mag", "version": "1.1.25"}, "adafruit_lsm6ds": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_lsm6ds", "pypi_name": "adafruit-circuitpython-lsm6ds", "repo": "https://github.com/adafruit/adafruit_circuitpython_lsm6ds", "version": "4.5.16"}, "adafruit_lsm9ds0": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_lsm9ds0", "pypi_name": "adafruit-circuitpython-lsm9ds0", "repo": "https://github.com/adafruit/adafruit_circuitpython_lsm9ds0", "version": "2.2.21"}, "adafruit_lsm9ds1": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_lsm9ds1", "pypi_name": "adafruit-circuitpython-lsm9ds1", "repo": "https://github.com/adafruit/adafruit_circuitpython_lsm9ds1", "version": "2.1.26"}, "adafruit_ltr329_ltr303": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ltr329_ltr303", "pypi_name": "adafruit-circuitpython-ltr329-ltr303", "repo": "https://github.com/adafruit/adafruit_circuitpython_ltr329_ltr303", "version": "3.0.10"}, "adafruit_ltr390": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ltr390", "pypi_name": "adafruit-circuitpython-ltr390", "repo": "https://github.com/adafruit/adafruit_circuitpython_ltr390", "version": "1.1.21"}, "adafruit_macropad": {"dependencies": ["adafruit_debouncer", "adafruit_display_text", "adafruit_hid", "adafruit_midi", "adafruit_simple_text_display", "neopixel"], "external_dependencies": [], "package": false, "path": "lib/adafruit_macropad", "pypi_name": "adafruit-circuitpython-macropad", "repo": "https://github.com/adafruit/adafruit_circuitpython_macropad", "version": "2.4.4"}, "adafruit_magtag": {"dependencies": ["adafruit_portalbase", "adafruit_requests", "neopixel", "simpleio"], "external_dependencies": [], "package": true, "path": "lib/adafruit_magtag", "pypi_name": "adafruit-circuitpython-magtag", "repo": "https://github.com/adafruit/adafruit_circuitpython_magtag", "version": "2.2.15"}, "adafruit_matrixkeypad": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_matrixkeypad", "pypi_name": "adafruit-circuitpython-matrixkeypad", "repo": "https://github.com/adafruit/adafruit_circuitpython_matrixkeypad", "version": "1.2.20"}, "adafruit_matrixportal": {"dependencies": ["adafruit_bitmap_font", "adafruit_display_text", "adafruit_esp32spi", "adafruit_io", "adafruit_portalbase", "adafruit_requests", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_matrixportal", "pypi_name": "adafruit-circuitpython-matrixportal", "repo": "https://github.com/adafruit/adafruit_circuitpython_matrixportal", "version": "3.2.7"}, "adafruit_max1704x": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_max1704x", "pypi_name": "adafruit-circuitpython-max1704x", "repo": "https://github.com/adafruit/adafruit_circuitpython_max1704x", "version": "1.0.14"}, "adafruit_max31855": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_max31855", "pypi_name": "adafruit-circuitpython-max31855", "repo": "https://github.com/adafruit/adafruit_circuitpython_max31855", "version": "3.2.25"}, "adafruit_max31856": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_max31856", "pypi_name": "adafruit-circuitpython-max31856", "repo": "https://github.com/adafruit/adafruit_circuitpython_max31856", "version": "0.12.4"}, "adafruit_max31865": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_max31865", "pypi_name": "adafruit-circuitpython-max31865", "repo": "https://github.com/adafruit/adafruit_circuitpython_max31865", "version": "2.2.25"}, "adafruit_max7219": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": true, "path": "lib/adafruit_max7219", "pypi_name": "adafruit-circuitpython-max7219", "repo": "https://github.com/adafruit/adafruit_circuitpython_max7219", "version": "1.5.18"}, "adafruit_max9744": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_max9744", "pypi_name": "adafruit-circuitpython-max9744", "repo": "https://github.com/adafruit/adafruit_circuitpython_max9744", "version": "1.2.21"}, "adafruit_mcp230xx": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_mcp230xx", "pypi_name": "adafruit-circuitpython-mcp230xx", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp230xx", "version": "2.5.18"}, "adafruit_mcp2515": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_mcp2515", "pypi_name": "adafruit-circuitpython-mcp2515", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp2515", "version": "1.1.10"}, "adafruit_mcp3421": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_mcp3421", "pypi_name": "adafruit-circuitpython-mcp3421", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp3421", "version": "1.1.4"}, "adafruit_mcp3xxx": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_mcp3xxx", "pypi_name": "adafruit-circuitpython-mcp3xxx", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp3xxx", "version": "1.4.21"}, "adafruit_mcp4725": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mcp4725", "pypi_name": "adafruit-circuitpython-mcp4725", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp4725", "version": "1.4.18"}, "adafruit_mcp4728": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_mcp4728", "pypi_name": "adafruit-circuitpython-mcp4728", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp4728", "version": "1.3.13"}, "adafruit_mcp9600": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mcp9600", "pypi_name": "adafruit-circuitpython-mcp9600", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp9600", "version": "2.0.7"}, "adafruit_mcp9808": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_mcp9808", "pypi_name": "adafruit-circuitpython-mcp9808", "repo": "https://github.com/adafruit/adafruit_circuitpython_mcp9808", "version": "3.3.28"}, "adafruit_midi": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_midi", "pypi_name": "adafruit-circuitpython-midi", "repo": "https://github.com/adafruit/adafruit_circuitpython_midi", "version": "1.5.5"}, "adafruit_midi_parser": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_midi_parser", "pypi_name": "adafruit-circuitpython-midi-parser", "repo": "https://github.com/adafruit/adafruit_circuitpython_midi_parser", "version": "1.2.0"}, "adafruit_miniesptool": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_miniesptool", "pypi_name": "adafruit-circuitpython-miniesptool", "repo": "https://github.com/adafruit/adafruit_circuitpython_miniesptool", "version": "0.2.24"}, "adafruit_minimqtt": {"dependencies": ["adafruit_connection_manager", "adafruit_ticks"], "external_dependencies": [], "package": true, "path": "lib/adafruit_minimqtt", "pypi_name": "adafruit-circuitpython-minimqtt", "repo": "https://github.com/adafruit/adafruit_circuitpython_minimqtt", "version": "8.0.2"}, "adafruit_miniqr": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_miniqr", "pypi_name": "adafruit-circuitpython-miniqr", "repo": "https://github.com/adafruit/adafruit_circuitpython_miniqr", "version": "2.1.6"}, "adafruit_mlx90393": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_mlx90393", "pypi_name": "adafruit-circuitpython-mlx90393", "repo": "https://github.com/adafruit/adafruit_circuitpython_mlx90393", "version": "2.3.6"}, "adafruit_mlx90395": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mlx90395", "pypi_name": "adafruit-circuitpython-mlx90395", "repo": "https://github.com/adafruit/adafruit_circuitpython_mlx90395", "version": "1.0.17"}, "adafruit_mlx90614": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mlx90614", "pypi_name": "adafruit-circuitpython-mlx90614", "repo": "https://github.com/adafruit/adafruit_circuitpython_mlx90614", "version": "1.2.22"}, "adafruit_mlx90640": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mlx90640", "pypi_name": "adafruit-circuitpython-mlx90640", "repo": "https://github.com/adafruit/adafruit_circuitpython_mlx90640", "version": "1.3.6"}, "adafruit_mma8451": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mma8451", "pypi_name": "adafruit-circuitpython-mma8451", "repo": "https://github.com/adafruit/adafruit_circuitpython_mma8451", "version": "1.3.25"}, "adafruit_mmc56x3": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mmc56x3", "pypi_name": "adafruit-circuitpython-mmc56x3", "repo": "https://github.com/adafruit/adafruit_circuitpython_mmc56x3", "version": "1.0.13"}, "adafruit_monsterm4sk": {"dependencies": ["adafruit_bus_device", "adafruit_lis3dh", "adafruit_register", "adafruit_seesaw", "adafruit_st7789"], "external_dependencies": [], "package": false, "path": "lib/adafruit_monsterm4sk", "pypi_name": "adafruit-circuitpython-monsterm4sk", "repo": "https://github.com/adafruit/adafruit_circuitpython_monsterm4sk", "version": "1.0.6"}, "adafruit_motor": {"dependencies": [], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_motor", "pypi_name": "adafruit-circuitpython-motor", "repo": "https://github.com/adafruit/adafruit_circuitpython_motor", "version": "3.4.17"}, "adafruit_motorkit": {"dependencies": ["adafruit_bus_device", "adafruit_motor", "adafruit_pca9685", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_motorkit", "pypi_name": "adafruit-circuitpython-motorkit", "repo": "https://github.com/adafruit/adafruit_circuitpython_motorkit", "version": "1.6.18"}, "adafruit_mpl115a2": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mpl115a2", "pypi_name": "adafruit-circuitpython-mpl115a2", "repo": "https://github.com/adafruit/adafruit_circuitpython_mpl115a2", "version": "1.1.21"}, "adafruit_mpl3115a2": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mpl3115a2", "pypi_name": "adafruit-circuitpython-mpl3115a2", "repo": "https://github.com/adafruit/adafruit_circuitpython_mpl3115a2", "version": "3.0.9"}, "adafruit_mpr121": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mpr121", "pypi_name": "adafruit-circuitpython-mpr121", "repo": "https://github.com/adafruit/adafruit_circuitpython_mpr121", "version": "2.1.24"}, "adafruit_mprls": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mprls", "pypi_name": "adafruit-circuitpython-mprls", "repo": "https://github.com/adafruit/adafruit_circuitpython_mprls", "version": "1.2.23"}, "adafruit_mpu6050": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_mpu6050", "pypi_name": "adafruit-circuitpython-mpu6050", "repo": "https://github.com/adafruit/adafruit_circuitpython_mpu6050", "version": "1.3.4"}, "adafruit_ms8607": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ms8607", "pypi_name": "adafruit-circuitpython-ms8607", "repo": "https://github.com/adafruit/adafruit_circuitpython_ms8607", "version": "1.1.5"}, "adafruit_msa3xx": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_msa3xx", "pypi_name": "adafruit-circuitpython-msa301", "repo": "https://github.com/adafruit/adafruit_circuitpython_msa301", "version": "1.3.10"}, "adafruit_neokey": {"dependencies": ["adafruit_seesaw"], "external_dependencies": [], "package": true, "path": "lib/adafruit_neokey", "pypi_name": "adafruit-circuitpython-neokey", "repo": "https://github.com/adafruit/adafruit_circuitpython_neokey", "version": "1.1.6"}, "adafruit_neopxl8": {"dependencies": ["adafruit_pioasm", "adafruit_pixelbuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_neopxl8", "pypi_name": "adafruit-circuitpython-neopxl8", "repo": "https://github.com/adafruit/adafruit_circuitpython_neopxl8", "version": "0.3.5"}, "adafruit_neotrellis": {"dependencies": ["adafruit_seesaw"], "external_dependencies": [], "package": true, "path": "lib/adafruit_neotrellis", "pypi_name": "adafruit-circuitpython-neotrellis", "repo": "https://github.com/adafruit/adafruit_circuitpython_neotrellis", "version": "1.3.10"}, "adafruit_ntp": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ntp", "pypi_name": "adafruit-circuitpython-ntp", "repo": "https://github.com/adafruit/adafruit_circuitpython_ntp", "version": "3.3.4"}, "adafruit_nunchuk": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_nunchuk", "pypi_name": "adafruit-circuitpython-nunchuk", "repo": "https://github.com/adafruit/adafruit_circuitpython_nunchuk", "version": "1.1.15"}, "adafruit_oauth2": {"dependencies": ["adafruit_requests"], "external_dependencies": [], "package": false, "path": "lib/adafruit_oauth2", "pypi_name": "adafruit-circuitpython-oauth2", "repo": "https://github.com/adafruit/adafruit_circuitpython_oauth2", "version": "1.0.22"}, "adafruit_onewire": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_onewire", "pypi_name": "adafruit-circuitpython-onewire", "repo": "https://github.com/adafruit/adafruit_circuitpython_onewire", "version": "2.0.11"}, "adafruit_opt4048": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_opt4048", "pypi_name": "adafruit-circuitpython-opt4048", "repo": "https://github.com/adafruit/adafruit_circuitpython_opt4048", "version": "1.0.3"}, "adafruit_ov2640": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ov2640", "pypi_name": "adafruit-circuitpython-ov2640", "repo": "https://github.com/adafruit/adafruit_circuitpython_ov2640", "version": "1.2.8"}, "adafruit_ov5640": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ov5640", "pypi_name": "adafruit-circuitpython-ov5640", "repo": "https://github.com/adafruit/adafruit_circuitpython_ov5640", "version": "1.2.10"}, "adafruit_ov7670": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ov7670", "pypi_name": "adafruit-circuitpython-ov7670", "repo": "https://github.com/adafruit/adafruit_circuitpython_ov7670", "version": "1.1.6"}, "adafruit_pastebin": {"dependencies": ["adafruit_requests"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pastebin", "pypi_name": "adafruit-circuitpython-pastebin", "repo": "https://github.com/adafruit/adafruit_circuitpython_pastebin", "version": "1.0.10"}, "adafruit_pathlib": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_pathlib", "pypi_name": "adafruit-circuitpython-pathlib", "repo": "https://github.com/adafruit/adafruit_circuitpython_pathlib", "version": "1.0.2"}, "adafruit_pca9554": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pca9554", "pypi_name": "adafruit-circuitpython-pca9554", "repo": "https://github.com/adafruit/adafruit_circuitpython_pca9554", "version": "1.0.5"}, "adafruit_pca9685": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pca9685", "pypi_name": "adafruit-circuitpython-pca9685", "repo": "https://github.com/adafruit/adafruit_circuitpython_pca9685", "version": "3.4.19"}, "adafruit_pcd8544": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pcd8544", "pypi_name": "adafruit-circuitpython-pcd8544", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcd8544", "version": "1.2.20"}, "adafruit_pcf8523": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pcf8523", "pypi_name": "adafruit-circuitpython-pcf8523", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcf8523", "version": "2.0.6"}, "adafruit_pcf8563": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pcf8563", "pypi_name": "adafruit-circuitpython-pcf8563", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcf8563", "version": "2.0.6"}, "adafruit_pcf8574": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pcf8574", "pypi_name": "adafruit-circuitpython-pcf8574", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcf8574", "version": "1.0.14"}, "adafruit_pcf8575": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pcf8575", "pypi_name": "adafruit-circuitpython-pcf8575", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcf8575", "version": "1.0.11"}, "adafruit_pcf8591": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["typing-extensions"], "package": true, "path": "lib/adafruit_pcf8591", "pypi_name": "adafruit-circuitpython-pcf8591", "repo": "https://github.com/adafruit/adafruit_circuitpython_pcf8591", "version": "1.0.22"}, "adafruit_pct2075": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pct2075", "pypi_name": "adafruit-circuitpython-pct2075", "repo": "https://github.com/adafruit/adafruit_circuitpython_pct2075", "version": "1.1.25"}, "adafruit_pio_uart": {"dependencies": ["adafruit_pioasm"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pio_uart", "pypi_name": "adafruit-circuitpython-pio-uart", "repo": "https://github.com/adafruit/adafruit_circuitpython_pio_uart", "version": "1.1.3"}, "adafruit_pioasm": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_pioasm", "pypi_name": "adafruit-circuitpython-pioasm", "repo": "https://github.com/adafruit/adafruit_circuitpython_pioasm", "version": "1.3.5"}, "adafruit_pixel_framebuf": {"dependencies": ["adafruit_framebuf", "adafruit_led_animation"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_pixel_framebuf", "pypi_name": "adafruit-circuitpython-pixel-framebuf", "repo": "https://github.com/adafruit/adafruit_circuitpython_pixel_framebuf", "version": "1.1.17"}, "adafruit_pixelbuf": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_pixelbuf", "pypi_name": "adafruit-circuitpython-pixelbuf", "repo": "https://github.com/adafruit/adafruit_circuitpython_pixelbuf", "version": "2.0.9"}, "adafruit_pixelmap": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_pixelmap", "pypi_name": "adafruit-circuitpython-pixelmap", "repo": "https://github.com/adafruit/adafruit_circuitpython_pixelmap", "version": "1.0.9"}, "adafruit_pixie": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_pixie", "pypi_name": "adafruit-circuitpython-pixie", "repo": "https://github.com/adafruit/adafruit_circuitpython_pixie", "version": "1.2.20"}, "adafruit_pm25": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pm25", "pypi_name": "adafruit-circuitpython-pm25", "repo": "https://github.com/adafruit/adafruit_circuitpython_pm25", "version": "2.1.21"}, "adafruit_pn532": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": true, "path": "lib/adafruit_pn532", "pypi_name": "adafruit-circuitpython-pn532", "repo": "https://github.com/adafruit/adafruit_circuitpython_pn532", "version": "2.4.5"}, "adafruit_portalbase": {"dependencies": ["adafruit_bitmap_font", "adafruit_connection_manager", "adafruit_display_text", "adafruit_fakerequests", "adafruit_imageload", "adafruit_io", "adafruit_miniqr", "adafruit_requests", "neopixel", "simpleio"], "external_dependencies": [], "package": true, "path": "lib/adafruit_portalbase", "pypi_name": "adafruit-circuitpython-portalbase", "repo": "https://github.com/adafruit/adafruit_circuitpython_portalbase", "version": "3.3.0"}, "adafruit_progressbar": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_progressbar", "pypi_name": "adafruit-circuitpython-progressbar", "repo": "https://github.com/adafruit/adafruit_circuitpython_progressbar", "version": "2.3.19"}, "adafruit_prompt_toolkit": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_prompt_toolkit", "pypi_name": "adafruit-circuitpython-prompt-toolkit", "repo": "https://github.com/adafruit/adafruit_circuitpython_prompt_toolkit", "version": "1.0.5"}, "adafruit_pybadger": {"dependencies": ["adafruit_bitmap_font", "adafruit_display_shapes", "adafruit_display_text", "adafruit_gizmo", "adafruit_lis3dh", "adafruit_lsm6ds", "adafruit_miniqr", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pybadger", "pypi_name": "adafruit-circuitpython-pybadger", "repo": "https://github.com/adafruit/adafruit_circuitpython_pybadger", "version": "4.0.6"}, "adafruit_pycamera": {"dependencies": ["adafruit_aw9523", "adafruit_bus_device", "adafruit_debouncer", "adafruit_display_text", "adafruit_lis3dh", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pycamera", "pypi_name": "adafruit-circuitpython-pycamera", "repo": "https://github.com/adafruit/adafruit_circuitpython_pycamera", "version": "1.5.2"}, "adafruit_pyoa": {"dependencies": ["adafruit_bitmap_font", "adafruit_button", "adafruit_cursorcontrol", "adafruit_display_text", "adafruit_touchscreen"], "external_dependencies": [], "package": false, "path": "lib/adafruit_pyoa", "pypi_name": "adafruit-circuitpython-pyoa", "repo": "https://github.com/adafruit/adafruit_circuitpython_pyoa", "version": "2.6.5"}, "adafruit_pyportal": {"dependencies": ["adafruit_bitmap_font", "adafruit_bus_device", "adafruit_esp32spi", "adafruit_portalbase", "adafruit_requests", "adafruit_touchscreen", "neopixel"], "external_dependencies": [], "package": true, "path": "lib/adafruit_pyportal", "pypi_name": "adafruit-circuitpython-pyportal", "repo": "https://github.com/adafruit/adafruit_circuitpython_pyportal", "version": "7.1.1"}, "adafruit_qmc5883p": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_qmc5883p", "pypi_name": "adafruit-circuitpython-qmc5883p", "repo": "https://github.com/adafruit/adafruit_circuitpython_qmc5883p", "version": "1.0.0"}, "adafruit_qualia": {"dependencies": ["adafruit_cst8xx", "adafruit_focaltouch", "adafruit_minimqtt", "adafruit_miniqr", "adafruit_pca9554", "adafruit_portalbase", "adafruit_requests"], "external_dependencies": [], "package": true, "path": "lib/adafruit_qualia", "pypi_name": "adafruit-circuitpython-qualia", "repo": "https://github.com/adafruit/adafruit_circuitpython_qualia", "version": "2.1.1"}, "adafruit_ra8875": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_ra8875", "pypi_name": "adafruit-circuitpython-ra8875", "repo": "https://github.com/adafruit/adafruit_circuitpython_ra8875", "version": "3.1.25"}, "adafruit_radial_controller": {"dependencies": ["adafruit_hid"], "external_dependencies": [], "package": true, "path": "lib/adafruit_radial_controller", "pypi_name": "adafruit-circuitpython-radial-controller", "repo": "https://github.com/adafruit/adafruit_circuitpython_radial_controller", "version": "1.0.16"}, "adafruit_register": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": true, "path": "lib/adafruit_register", "pypi_name": "adafruit-circuitpython-register", "repo": "https://github.com/adafruit/adafruit_circuitpython_register", "version": "1.10.4"}, "adafruit_requests": {"dependencies": ["adafruit_connection_manager"], "external_dependencies": [], "package": false, "path": "lib/adafruit_requests", "pypi_name": "adafruit-circuitpython-requests", "repo": "https://github.com/adafruit/adafruit_circuitpython_requests", "version": "4.1.13"}, "adafruit_rfm": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["asyncio"], "package": true, "path": "lib/adafruit_rfm", "pypi_name": "adafruit-circuitpython-rfm", "repo": "https://github.com/adafruit/adafruit_circuitpython_rfm", "version": "1.0.7"}, "adafruit_rfm69": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_rfm69", "pypi_name": "adafruit-circuitpython-rfm69", "repo": "https://github.com/adafruit/adafruit_circuitpython_rfm69", "version": "2.1.25"}, "adafruit_rfm9x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_rfm9x", "pypi_name": "adafruit-circuitpython-rfm9x", "repo": "https://github.com/adafruit/adafruit_circuitpython_rfm9x", "version": "2.2.20"}, "adafruit_rgb_display": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_rgb_display", "pypi_name": "adafruit-circuitpython-rgb-display", "repo": "https://github.com/adafruit/adafruit_circuitpython_rgb_display", "version": "3.14.1"}, "adafruit_rgbled": {"dependencies": ["adafruit_pca9685"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_rgbled", "pypi_name": "adafruit-circuitpython-rgbled", "repo": "https://github.com/adafruit/adafruit_circuitpython_rgbled", "version": "1.2.4"}, "adafruit_rockblock": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_rockblock", "pypi_name": "adafruit-circuitpython-rockblock", "repo": "https://github.com/adafruit/adafruit_circuitpython_rockblock", "version": "1.3.19"}, "adafruit_rplidar": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_rplidar", "pypi_name": "adafruit-circuitpython-rplidar", "repo": "https://github.com/adafruit/adafruit_circuitpython_rplidar", "version": "1.2.18"}, "adafruit_rsa": {"dependencies": ["adafruit_hashlib", "adafruit_logging"], "external_dependencies": [], "package": true, "path": "lib/adafruit_rsa", "pypi_name": "adafruit-circuitpython-rsa", "repo": "https://github.com/adafruit/adafruit_circuitpython_rsa", "version": "1.2.27"}, "adafruit_rtttl": {"dependencies": ["adafruit_waveform"], "external_dependencies": [], "package": false, "path": "lib/adafruit_rtttl", "pypi_name": "adafruit-circuitpython-rtttl", "repo": "https://github.com/adafruit/adafruit_circuitpython_rtttl", "version": "2.4.26"}, "adafruit_s35710": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_s35710", "pypi_name": "adafruit-circuitpython-s35710", "repo": "https://github.com/adafruit/adafruit_circuitpython_s35710", "version": "1.0.5"}, "adafruit_scd30": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_scd30", "pypi_name": "adafruit-circuitpython-scd30", "repo": "https://github.com/adafruit/adafruit_circuitpython_scd30", "version": "2.2.16"}, "adafruit_scd4x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_scd4x", "pypi_name": "adafruit-circuitpython-scd4x", "repo": "https://github.com/adafruit/adafruit_circuitpython_scd4x", "version": "1.4.8"}, "adafruit_sdcard": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_sdcard", "pypi_name": "adafruit-circuitpython-sd", "repo": "https://github.com/adafruit/adafruit_circuitpython_sd", "version": "3.3.27"}, "adafruit_seesaw": {"dependencies": ["adafruit_bus_device", "adafruit_pixelbuf"], "external_dependencies": [], "package": true, "path": "lib/adafruit_seesaw", "pypi_name": "adafruit-circuitpython-seesaw", "repo": "https://github.com/adafruit/adafruit_circuitpython_seesaw", "version": "1.16.7"}, "adafruit_sen6x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_sen6x", "pypi_name": "adafruit-circuitpython-sen6x", "repo": "https://github.com/adafruit/adafruit_circuitpython_sen6x", "version": "1.0.1"}, "adafruit_servokit": {"dependencies": ["adafruit_bus_device", "adafruit_motor", "adafruit_pca9685", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_servokit", "pypi_name": "adafruit-circuitpython-servokit", "repo": "https://github.com/adafruit/adafruit_circuitpython_servokit", "version": "1.3.21"}, "adafruit_sgp30": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_sgp30", "pypi_name": "adafruit-circuitpython-sgp30", "repo": "https://github.com/adafruit/adafruit_circuitpython_sgp30", "version": "3.0.13"}, "adafruit_sgp40": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": true, "path": "lib/adafruit_sgp40", "pypi_name": "adafruit-circuitpython-sgp40", "repo": "https://github.com/adafruit/adafruit_circuitpython_sgp40", "version": "1.3.21"}, "adafruit_sharpmemorydisplay": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_sharpmemorydisplay", "pypi_name": "adafruit-circuitpython-sharpmemorydisplay", "repo": "https://github.com/adafruit/adafruit_circuitpython_sharpmemorydisplay", "version": "1.4.16"}, "adafruit_sht31d": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": false, "path": "lib/adafruit_sht31d", "pypi_name": "adafruit-circuitpython-sht31d", "repo": "https://github.com/adafruit/adafruit_circuitpython_sht31d", "version": "2.3.28"}, "adafruit_sht4x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_sht4x", "pypi_name": "adafruit-circuitpython-sht4x", "repo": "https://github.com/adafruit/adafruit_circuitpython_sht4x", "version": "1.0.24"}, "adafruit_shtc3": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_shtc3", "pypi_name": "adafruit-circuitpython-shtc3", "repo": "https://github.com/adafruit/adafruit_circuitpython_shtc3", "version": "1.1.19"}, "adafruit_si1145": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_si1145", "pypi_name": "adafruit-circuitpython-si1145", "repo": "https://github.com/adafruit/adafruit_circuitpython_si1145", "version": "1.2.5"}, "adafruit_si4713": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_si4713", "pypi_name": "adafruit-circuitpython-si4713", "repo": "https://github.com/adafruit/adafruit_circuitpython_si4713", "version": "1.3.18"}, "adafruit_si5351": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_si5351", "pypi_name": "adafruit-circuitpython-si5351", "repo": "https://github.com/adafruit/adafruit_circuitpython_si5351", "version": "1.4.8"}, "adafruit_si7021": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": true, "path": "lib/adafruit_si7021", "pypi_name": "adafruit-circuitpython-si7021", "repo": "https://github.com/adafruit/adafruit_circuitpython_si7021", "version": "4.1.16"}, "adafruit_simple_text_display": {"dependencies": ["adafruit_display_text"], "external_dependencies": [], "package": false, "path": "lib/adafruit_simple_text_display", "pypi_name": "adafruit-circuitpython-simple-text-display", "repo": "https://github.com/adafruit/adafruit_circuitpython_simple_text_display", "version": "1.2.20"}, "adafruit_simplemath": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_simplemath", "pypi_name": "adafruit-circuitpython-simplemath", "repo": "https://github.com/adafruit/adafruit_circuitpython_simplemath", "version": "2.0.16"}, "adafruit_slideshow": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_slideshow", "pypi_name": "adafruit-circuitpython-slideshow", "repo": "https://github.com/adafruit/adafruit_circuitpython_slideshow", "version": "1.8.6"}, "adafruit_spd1656": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_spd1656", "pypi_name": "adafruit-circuitpython-spd1656", "repo": "https://github.com/adafruit/adafruit_circuitpython_spd1656", "version": "0.9.5"}, "adafruit_ssd1305": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1305", "pypi_name": "adafruit-circuitpython-ssd1305", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1305", "version": "1.4.3"}, "adafruit_ssd1306": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1306", "pypi_name": "adafruit-circuitpython-ssd1306", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1306", "version": "2.12.21"}, "adafruit_ssd1322": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1322", "pypi_name": "adafruit-circuitpython-ssd1322", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1322", "version": "1.4.7"}, "adafruit_ssd1325": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1325", "pypi_name": "adafruit-circuitpython-ssd1325", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1325", "version": "2.0.1"}, "adafruit_ssd1327": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1327", "pypi_name": "adafruit-circuitpython-ssd1327", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1327", "version": "1.4.7"}, "adafruit_ssd1331": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1331", "pypi_name": "adafruit-circuitpython-ssd1331", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1331", "version": "2.0.1"}, "adafruit_ssd1351": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1351", "pypi_name": "adafruit-circuitpython-ssd1351", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1351", "version": "1.4.6"}, "adafruit_ssd1608": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1608", "pypi_name": "adafruit-circuitpython-ssd1608", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1608", "version": "1.3.7"}, "adafruit_ssd1675": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1675", "pypi_name": "adafruit-circuitpython-ssd1675", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1675", "version": "2.0.2"}, "adafruit_ssd1680": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1680", "pypi_name": "adafruit-circuitpython-ssd1680", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1680", "version": "2.1.0"}, "adafruit_ssd1681": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ssd1681", "pypi_name": "adafruit-circuitpython-ssd1681", "repo": "https://github.com/adafruit/adafruit_circuitpython_ssd1681", "version": "2.0.2"}, "adafruit_st7565": {"dependencies": ["adafruit_bus_device", "adafruit_framebuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_st7565", "pypi_name": "adafruit-circuitpython-st7565", "repo": "https://github.com/adafruit/adafruit_circuitpython_st7565", "version": "1.1.13"}, "adafruit_st7735": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_st7735", "pypi_name": "adafruit-circuitpython-st7735", "repo": "https://github.com/adafruit/adafruit_circuitpython_st7735", "version": "1.2.19"}, "adafruit_st7735r": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_st7735r", "pypi_name": "adafruit-circuitpython-st7735r", "repo": "https://github.com/adafruit/adafruit_circuitpython_st7735r", "version": "2.0.1"}, "adafruit_st7789": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_st7789", "pypi_name": "adafruit-circuitpython-st7789", "repo": "https://github.com/adafruit/adafruit_circuitpython_st7789", "version": "2.1.2"}, "adafruit_stmpe610": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_stmpe610", "pypi_name": "adafruit-circuitpython-stmpe610", "repo": "https://github.com/adafruit/adafruit_circuitpython_stmpe610", "version": "1.3.19"}, "adafruit_stspin": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_stspin", "pypi_name": "adafruit-circuitpython-stspin", "repo": "https://github.com/adafruit/adafruit_circuitpython_stspin", "version": "1.0.0"}, "adafruit_tc74": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tc74", "pypi_name": "adafruit-circuitpython-tc74", "repo": "https://github.com/adafruit/adafruit_circuitpython_tc74", "version": "1.0.21"}, "adafruit_tca8418": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tca8418", "pypi_name": "adafruit-circuitpython-tca8418", "repo": "https://github.com/adafruit/adafruit_circuitpython_tca8418", "version": "1.0.18"}, "adafruit_tca9548a": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": false, "path": "lib/adafruit_tca9548a", "pypi_name": "adafruit-circuitpython-tca9548a", "repo": "https://github.com/adafruit/adafruit_circuitpython_tca9548a", "version": "0.8.2"}, "adafruit_tcs34725": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tcs34725", "pypi_name": "adafruit-circuitpython-tcs34725", "repo": "https://github.com/adafruit/adafruit_circuitpython_tcs34725", "version": "3.3.25"}, "adafruit_templateengine": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_templateengine", "pypi_name": "adafruit-circuitpython-templateengine", "repo": "https://github.com/adafruit/adafruit_circuitpython_templateengine", "version": "2.0.5"}, "adafruit_tfmini": {"dependencies": [], "external_dependencies": ["adafruit-circuitpython-typing", "typing-extensions"], "package": false, "path": "lib/adafruit_tfmini", "pypi_name": "adafruit-circuitpython-tfmini", "repo": "https://github.com/adafruit/adafruit_circuitpython_tfmini", "version": "1.2.22"}, "adafruit_thermistor": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_thermistor", "pypi_name": "adafruit-circuitpython-thermistor", "repo": "https://github.com/adafruit/adafruit_circuitpython_thermistor", "version": "3.4.15"}, "adafruit_ticks": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_ticks", "pypi_name": "adafruit-circuitpython-ticks", "repo": "https://github.com/adafruit/adafruit_circuitpython_ticks", "version": "1.1.4"}, "adafruit_tinylora": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing_extensions"], "package": true, "path": "lib/adafruit_tinylora", "pypi_name": "adafruit-circuitpython-tinylora", "repo": "https://github.com/adafruit/adafruit_circuitpython_tinylora", "version": "2.2.22"}, "adafruit_tla202x": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": true, "path": "lib/adafruit_tla202x", "pypi_name": "adafruit-circuitpython-tla202x", "repo": "https://github.com/adafruit/adafruit_circuitpython_tla202x", "version": "1.0.20"}, "adafruit_tlc5947": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_tlc5947", "pypi_name": "adafruit-circuitpython-tlc5947", "repo": "https://github.com/adafruit/adafruit_circuitpython_tlc5947", "version": "1.3.20"}, "adafruit_tlc59711": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_tlc59711", "pypi_name": "adafruit-circuitpython-tlc59711", "repo": "https://github.com/adafruit/adafruit_circuitpython_tlc59711", "version": "2.0.17"}, "adafruit_tlv320": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tlv320", "pypi_name": "adafruit-circuitpython-tlv320", "repo": "https://github.com/adafruit/adafruit_circuitpython_tlv320", "version": "1.1.0"}, "adafruit_tlv493d": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tlv493d", "pypi_name": "adafruit-circuitpython-tlv493d", "repo": "https://github.com/adafruit/adafruit_circuitpython_tlv493d", "version": "2.0.9"}, "adafruit_tm1814": {"dependencies": ["adafruit_pioasm", "adafruit_pixelbuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tm1814", "pypi_name": "adafruit-circuitpython-tm1814", "repo": "https://github.com/adafruit/adafruit_circuitpython_tm1814", "version": "1.0.1"}, "adafruit_tmp006": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tmp006", "pypi_name": "adafruit-circuitpython-tmp006", "repo": "https://github.com/adafruit/adafruit_circuitpython_tmp006", "version": "2.1.22"}, "adafruit_tmp007": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tmp007", "pypi_name": "adafruit-circuitpython-tmp007", "repo": "https://github.com/adafruit/adafruit_circuitpython_tmp007", "version": "2.1.19"}, "adafruit_tmp117": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tmp117", "pypi_name": "adafruit-circuitpython-tmp117", "repo": "https://github.com/adafruit/adafruit_circuitpython_tmp117", "version": "1.1.17"}, "adafruit_touchscreen": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_touchscreen", "pypi_name": "adafruit-circuitpython-touchscreen", "repo": "https://github.com/adafruit/adafruit_circuitpython_touchscreen", "version": "1.3.5"}, "adafruit_tpa2016": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tpa2016", "pypi_name": "adafruit-circuitpython-tpa2016", "repo": "https://github.com/adafruit/adafruit_circuitpython_tpa2016", "version": "1.1.20"}, "adafruit_trellis": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_trellis", "pypi_name": "adafruit-circuitpython-trellis", "repo": "https://github.com/adafruit/adafruit_circuitpython_trellis", "version": "1.3.23"}, "adafruit_trellism4": {"dependencies": ["adafruit_matrixkeypad", "neopixel"], "external_dependencies": ["typing-extensions"], "package": false, "path": "lib/adafruit_trellism4", "pypi_name": "adafruit-circuitpython-trellism4", "repo": "https://github.com/adafruit/adafruit_circuitpython_trellism4", "version": "1.5.22"}, "adafruit_tsc2007": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tsc2007", "pypi_name": "adafruit-circuitpython-tsc2007", "repo": "https://github.com/adafruit/adafruit_circuitpython_tsc2007", "version": "1.1.5"}, "adafruit_tsl2561": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tsl2561", "pypi_name": "adafruit-circuitpython-tsl2561", "repo": "https://github.com/adafruit/adafruit_circuitpython_tsl2561", "version": "3.3.22"}, "adafruit_tsl2591": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tsl2591", "pypi_name": "adafruit-circuitpython-tsl2591", "repo": "https://github.com/adafruit/adafruit_circuitpython_tsl2591", "version": "1.4.4"}, "adafruit_tt21100": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_tt21100", "pypi_name": "adafruit-circuitpython-tt21100", "repo": "https://github.com/adafruit/adafruit_circuitpython_tt21100", "version": "1.0.6"}, "adafruit_turtle": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_turtle", "pypi_name": "adafruit-circuitpython-turtle", "repo": "https://github.com/adafruit/adafruit_circuitpython_turtle", "version": "3.1.8"}, "adafruit_uc8151d": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_uc8151d", "pypi_name": "adafruit-circuitpython-uc8151d", "repo": "https://github.com/adafruit/adafruit_circuitpython_uc8151d", "version": "2.0.2"}, "adafruit_us100": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_us100", "pypi_name": "adafruit-circuitpython-us100", "repo": "https://github.com/adafruit/adafruit_circuitpython_us100", "version": "1.1.21"}, "adafruit_usb_host_descriptors": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_usb_host_descriptors", "pypi_name": "adafruit-circuitpython-usb-host-descriptors", "repo": "https://github.com/adafruit/adafruit_circuitpython_usb_host_descriptors", "version": "0.3.1"}, "adafruit_usb_host_mass_storage": {"dependencies": ["adafruit_usb_host_descriptors"], "external_dependencies": [], "package": false, "path": "lib/adafruit_usb_host_mass_storage", "pypi_name": "adafruit-circuitpython-usb-host-mass-storage", "repo": "https://github.com/adafruit/adafruit_circuitpython_usb_host_mass_storage", "version": "0.8.3"}, "adafruit_usb_host_midi": {"dependencies": ["adafruit_usb_host_descriptors"], "external_dependencies": ["pyusb"], "package": false, "path": "lib/adafruit_usb_host_midi", "pypi_name": "adafruit-circuitpython-usb-host-midi", "repo": "https://github.com/adafruit/adafruit_circuitpython_usb_host_midi", "version": "0.8.3"}, "adafruit_usb_host_mouse": {"dependencies": ["adafruit_usb_host_descriptors"], "external_dependencies": [], "package": false, "path": "lib/adafruit_usb_host_mouse", "pypi_name": "adafruit-circuitpython-usb-host-mouse", "repo": "https://github.com/adafruit/adafruit_circuitpython_usb_host_mouse", "version": "1.2.0"}, "adafruit_vc0706": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vc0706", "pypi_name": "adafruit-circuitpython-vc0706", "repo": "https://github.com/adafruit/adafruit_circuitpython_vc0706", "version": "6.0.14"}, "adafruit_vcnl4010": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vcnl4010", "pypi_name": "adafruit-circuitpython-vcnl4010", "repo": "https://github.com/adafruit/adafruit_circuitpython_vcnl4010", "version": "0.11.15"}, "adafruit_vcnl4020": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vcnl4020", "pypi_name": "adafruit-circuitpython-vcnl4020", "repo": "https://github.com/adafruit/adafruit_circuitpython_vcnl4020", "version": "1.0.5"}, "adafruit_vcnl4040": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vcnl4040", "pypi_name": "adafruit-circuitpython-vcnl4040", "repo": "https://github.com/adafruit/adafruit_circuitpython_vcnl4040", "version": "1.2.22"}, "adafruit_vcnl4200": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vcnl4200", "pypi_name": "adafruit-circuitpython-vcnl4200", "repo": "https://github.com/adafruit/adafruit_circuitpython_vcnl4200", "version": "1.0.1"}, "adafruit_veml6070": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_veml6070", "pypi_name": "adafruit-circuitpython-veml6070", "repo": "https://github.com/adafruit/adafruit_circuitpython_veml6070", "version": "3.1.24"}, "adafruit_veml6075": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_veml6075", "pypi_name": "adafruit-circuitpython-veml6075", "repo": "https://github.com/adafruit/adafruit_circuitpython_veml6075", "version": "1.1.22"}, "adafruit_veml7700": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/adafruit_veml7700", "pypi_name": "adafruit-circuitpython-veml7700", "repo": "https://github.com/adafruit/adafruit_circuitpython_veml7700", "version": "2.1.4"}, "adafruit_vl53l0x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vl53l0x", "pypi_name": "adafruit-circuitpython-vl53l0x", "repo": "https://github.com/adafruit/adafruit_circuitpython_vl53l0x", "version": "3.6.16"}, "adafruit_vl53l1x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vl53l1x", "pypi_name": "adafruit-circuitpython-vl53l1x", "repo": "https://github.com/adafruit/adafruit_circuitpython_vl53l1x", "version": "1.2.5"}, "adafruit_vl53l4cd": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vl53l4cd", "pypi_name": "adafruit-circuitpython-vl53l4cd", "repo": "https://github.com/adafruit/adafruit_circuitpython_vl53l4cd", "version": "1.3.3"}, "adafruit_vl6180x": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_vl6180x", "pypi_name": "adafruit-circuitpython-vl6180x", "repo": "https://github.com/adafruit/adafruit_circuitpython_vl6180x", "version": "1.4.18"}, "adafruit_vs1053": {"dependencies": ["adafruit_bus_device"], "external_dependencies": ["adafruit-circuitpython-typing"], "package": false, "path": "lib/adafruit_vs1053", "pypi_name": "adafruit-circuitpython-vs1053", "repo": "https://github.com/adafruit/adafruit_circuitpython_vs1053", "version": "1.2.23"}, "adafruit_wave": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/adafruit_wave", "pypi_name": "adafruit-circuitpython-wave", "repo": "https://github.com/adafruit/adafruit_circuitpython_wave", "version": "0.0.7"}, "adafruit_waveform": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_waveform", "pypi_name": "adafruit-circuitpython-waveform", "repo": "https://github.com/adafruit/adafruit_circuitpython_waveform", "version": "1.3.24"}, "adafruit_wii_classic": {"dependencies": ["adafruit_bus_device"], "external_dependencies": [], "package": false, "path": "lib/adafruit_wii_classic", "pypi_name": "adafruit-circuitpython-wii-classic", "repo": "https://github.com/adafruit/adafruit_circuitpython_wii_classic", "version": "1.0.10"}, "adafruit_wiz": {"dependencies": ["adafruit_connection_manager"], "external_dependencies": [], "package": false, "path": "lib/adafruit_wiz", "pypi_name": "adafruit-circuitpython-wiz", "repo": "https://github.com/adafruit/adafruit_circuitpython_wiz", "version": "1.1.1"}, "adafruit_wiznet5k": {"dependencies": ["adafruit_bus_device", "adafruit_ticks"], "external_dependencies": [], "package": true, "path": "lib/adafruit_wiznet5k", "pypi_name": "adafruit-circuitpython-wiznet5k", "repo": "https://github.com/adafruit/adafruit_circuitpython_wiznet5k", "version": "7.2.4"}, "adafruit_wm8960": {"dependencies": ["adafruit_bus_device", "adafruit_simplemath"], "external_dependencies": [], "package": true, "path": "lib/adafruit_wm8960", "pypi_name": "adafruit-circuitpython-wm8960", "repo": "https://github.com/adafruit/adafruit_circuitpython_wm8960", "version": "1.0.4"}, "adafruit_ws2801": {"dependencies": ["adafruit_bus_device", "adafruit_pixelbuf"], "external_dependencies": [], "package": false, "path": "lib/adafruit_ws2801", "pypi_name": "adafruit-circuitpython-ws2801", "repo": "https://github.com/adafruit/adafruit_circuitpython_ws2801", "version": "1.0.3"}, "adafruit_wsgi": {"dependencies": [], "external_dependencies": [], "package": true, "path": "lib/adafruit_wsgi", "pypi_name": "adafruit-circuitpython-wsgi", "repo": "https://github.com/adafruit/adafruit_circuitpython_wsgi", "version": "3.0.2"}, "asyncio": {"dependencies": ["adafruit_ticks"], "external_dependencies": [], "package": true, "path": "lib/asyncio", "pypi_name": "adafruit-circuitpython-asyncio", "repo": "https://github.com/adafruit/adafruit_circuitpython_asyncio", "version": "3.0.2"}, "cedargrove_nau7802": {"dependencies": ["adafruit_bus_device", "adafruit_register"], "external_dependencies": [], "package": false, "path": "lib/cedargrove_nau7802", "pypi_name": "circuitpython-nau7802", "repo": "https://github.com/adafruit/circuitpython_nau7802", "version": "2.1.2"}, "colorsys": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/colorsys", "pypi_name": "adafruit-circuitpython-colorsys", "repo": "https://github.com/adafruit/adafruit_circuitpython_colorsys", "version": "3.0.3"}, "neopixel": {"dependencies": ["adafruit_pixelbuf"], "external_dependencies": [], "package": false, "path": "lib/neopixel", "pypi_name": "adafruit-circuitpython-neopixel", "repo": "https://github.com/adafruit/adafruit_circuitpython_neopixel", "version": "6.3.17"}, "neopixel_spi": {"dependencies": ["adafruit_bus_device", "adafruit_pixelbuf"], "external_dependencies": [], "package": false, "path": "lib/neopixel_spi", "pypi_name": "adafruit-circuitpython-neopixel-spi", "repo": "https://github.com/adafruit/adafruit_circuitpython_neopixel_spi", "version": "1.0.13"}, "simpleio": {"dependencies": [], "external_dependencies": [], "package": false, "path": "lib/simpleio", "pypi_name": "adafruit-circuitpython-simpleio", "repo": "https://github.com/adafruit/adafruit_circuitpython_simpleio", "version": "3.0.17"}}