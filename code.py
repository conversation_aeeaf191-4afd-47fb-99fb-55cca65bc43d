
import time
import board
import digitalio
import pwmio
import neopixel
from adafruit_motor import servo

# --- Pin Configuration ---
# Inputs for triggers
# Connect a wire from the 3.3V pin to these pins to trigger them.
pin_13_in = digitalio.DigitalInOut(board.D13)
pin_13_in.direction = digitalio.Direction.INPUT
pin_13_in.pull = digitalio.Pull.DOWN

pin_12_in = digitalio.DigitalInOut(board.D12)
pin_12_in.direction = digitalio.Direction.INPUT
pin_12_in.pull = digitalio.Pull.DOWN

pin_11_in = digitalio.DigitalInOut(board.D11)
pin_11_in.direction = digitalio.Direction.INPUT
pin_11_in.pull = digitalio.Pull.DOWN

# --- NeoPixel LED Configuration ---
# The onboard NeoPixel on the Feather RP2040
pixel = neopixel.NeoPixel(board.NEOPIXEL, 1, brightness=0.01, auto_write=True)
pixel.fill((0, 0, 0)) # Start with the LED off

# A list of colors to cycle through
colors = [
    (255, 0, 0),    # Red
    (0, 255, 0),    # <PERSON>
    (0, 0, 255),    # <PERSON>
    (255, 255, 0),  # Yellow
    (0, 255, 255),  # <PERSON><PERSON>
    (255, 0, 255),  # Magenta
]

# PWM Output for Servo
# Connect the servo's signal wire to this pin.
pwm = pwmio.PWMOut(board.D5, duty_cycle=2 ** 15, frequency=50)

# Create a servo object
my_servo = servo.Servo(pwm)

# --- Main Loop ---
def main():
    """Main loop to check inputs, control the servo, and blink the LED."""
    print("Servo control script running. Waiting for input on D11, D12, or D13.")
    print("Onboard LED will cycle colors, turning off for 1s between each color.")

    # --- LED State Variables ---
    last_state_change_time = time.monotonic()
    color_index = 0
    led_is_on = True
    pixel.fill(colors[color_index]) # Set initial color

    while True:
        # --- Non-blocking LED Blinking Logic ---
        # This logic shows a color for 7 seconds, then turns the LED off
        # for 1 second before cycling to the next color.
        current_time = time.monotonic()
        if led_is_on:
            # If LED is on, check if 7 seconds have passed to turn it off.
            if current_time - last_state_change_time >= 7.0:
                last_state_change_time = current_time
                pixel.fill((0, 0, 0))  # Turn LED off
                led_is_on = False
        else:  # LED is off
            # If LED is off, check if 1 second has passed to turn it on with the next color.
            if current_time - last_state_change_time >= 1.0:
                last_state_change_time = current_time
                color_index = (color_index + 1) % len(colors)
                pixel.fill(colors[color_index])  # Turn on with next color
                led_is_on = True

        # --- Servo Control Logic ---
        # Note: The time.sleep() calls inside these blocks are "blocking".
        # This means while the servo is moving or holding, the LED color
        # will not update. The blinking will appear to pause.
        if pin_13_in.value:
            print("Pin 13 triggered: Continuous spin.")
            # Sweep from 0 to 180 degrees and back
            for angle in range(0, 180, 5):
                my_servo.angle = angle
                time.sleep(0.05)
            for angle in range(180, 0, -5):
                my_servo.angle = angle
                time.sleep(0.05)

        elif pin_12_in.value:
            print("Pin 12 triggered: Half turn (90 degrees).")
            my_servo.angle = 90
            time.sleep(1) # Hold the position for a second
            my_servo.angle = 0 # Return to start
            time.sleep(1)


        elif pin_11_in.value:
            print("Pin 11 triggered: Quarter turn (45 degrees).")
            my_servo.angle = 45
            time.sleep(1) # Hold the position for a second
            my_servo.angle = 0 # Return to start
            time.sleep(1)

        else:
            # Optional: Reset servo to 0 when no signal is active
            my_servo.angle = 0

        # The main loop delay. The LED blink check happens more frequently
        # than this, but this prevents the CPU from running at 100% when idle.
        time.sleep(0.01)

if __name__ == "__main__":
    main()
