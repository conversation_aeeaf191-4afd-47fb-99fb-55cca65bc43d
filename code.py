
import time
import board
import digitalio
import pwmio
import neopixel
import random
from adafruit_motor import servo

# --- Pin Configuration ---
# Inputs for triggers
# Connect a wire from the 3.3V pin to these pins to trigger them.
pin_13_in = digitalio.DigitalInOut(board.D13)
pin_13_in.direction = digitalio.Direction.INPUT
pin_13_in.pull = digitalio.Pull.DOWN

pin_12_in = digitalio.DigitalInOut(board.D12)
pin_12_in.direction = digitalio.Direction.INPUT
pin_12_in.pull = digitalio.Pull.DOWN

pin_11_in = digitalio.DigitalInOut(board.D11)
pin_11_in.direction = digitalio.Direction.INPUT
pin_11_in.pull = digitalio.Pull.DOWN

# --- NeoPixel LED Configuration ---
# The onboard NeoPixel on the Feather RP2040
pixel = neopixel.NeoPixel(board.NEOPIXEL, 1, brightness=0.01, auto_write=True)
pixel.fill((0, 0, 0)) # Start with the LED off

# A list of colors to cycle through
colors = [
    (255, 0, 0),    # Red
    (0, 255, 0),    # <PERSON>
    (0, 0, 255),    # <PERSON>
    (255, 255, 0),  # Yellow
    (0, 255, 255),  # <PERSON><PERSON>
    (255, 0, 255),  # Magenta
]

# PWM Output for Servo
# Connect the servo's signal wire to this pin.
pwm = pwmio.PWMOut(board.D5, duty_cycle=2 ** 15, frequency=50)

# Create a servo object
my_servo = servo.Servo(pwm)

def handle_pin_12_clicks(click_count):
    """Handle different click patterns for pin 12."""

    if click_count == 1:
        print("Pin 12: Single click - 360 degree rotation")
        # Do a full 360 degree rotation
        for angle in range(0, 360, 5):
            my_servo.angle = angle % 180  # Servo range is 0-180, so we'll do two full sweeps
            time.sleep(0.02)
        for angle in range(0, 360, 5):
            my_servo.angle = angle % 180
            time.sleep(0.02)
        my_servo.angle = 0  # Return to start

    elif click_count == 2:
        print("Pin 12: Double click - 180 degrees fast and back")
        # Fast 180 to one side and back
        my_servo.angle = 180
        time.sleep(0.5)
        my_servo.angle = 0
        time.sleep(0.5)

    elif click_count == 3:
        print("Pin 12: Triple click - Random twist")
        # Random twist pattern
        for _ in range(5):
            random_angle = random.randint(0, 180)
            my_servo.angle = random_angle
            time.sleep(random.uniform(0.1, 0.3))
        my_servo.angle = 0  # Return to start

# --- Main Loop ---
def main():
    """Main loop to check inputs, control the servo, and blink the LED."""
    print("Servo control script running. Waiting for input on D11, D12, or D13.")
    print("Onboard LED will cycle colors, turning off for 1s between each color.")

    # --- LED State Variables ---
    last_state_change_time = time.monotonic()
    color_index = 0
    led_is_on = True
    pixel.fill(colors[color_index]) # Set initial color

    # --- Pin 12 Click Detection Variables (local to main) ---
    pin_12_last_state = False
    pin_12_click_count = 0
    pin_12_first_click_time = 0
    pin_12_press_start_time = 0
    pin_12_is_held = False

    while True:
        # --- Non-blocking LED Blinking Logic ---
        # This logic shows a color for 7 seconds, then turns the LED off
        # for 1 second before cycling to the next color.
        current_time = time.monotonic()
        if led_is_on:
            # If LED is on, check if 7 seconds have passed to turn it off.
            if current_time - last_state_change_time >= 7.0:
                last_state_change_time = current_time
                pixel.fill((0, 0, 0))  # Turn LED off
                led_is_on = False
        else:  # LED is off
            # If LED is off, check if 1 second has passed to turn it on with the next color.
            if current_time - last_state_change_time >= 1.0:
                last_state_change_time = current_time
                color_index = (color_index + 1) % len(colors)
                pixel.fill(colors[color_index])  # Turn on with next color
                led_is_on = True

        # --- Servo Control Logic ---
        # Note: The time.sleep() calls inside these blocks are "blocking".
        # This means while the servo is moving or holding, the LED color
        # will not update. The blinking will appear to pause.
        if pin_13_in.value:
            print("Pin 13 triggered: Continuous spin.")
            # Sweep from 0 to 180 degrees and back
            for angle in range(0, 180, 5):
                my_servo.angle = angle
                time.sleep(0.05)
            for angle in range(180, 0, -5):
                my_servo.angle = angle
                time.sleep(0.05)

        # --- Pin 12 Click Detection Logic ---
        current_pin_12_state = pin_12_in.value
        current_time = time.monotonic()

        # Detect state changes (press/release)
        if current_pin_12_state != pin_12_last_state:
            if current_pin_12_state:  # Button pressed
                pin_12_press_start_time = current_time
                pin_12_is_held = False

                # Check if this is part of a multi-click sequence
                if pin_12_click_count == 0:
                    pin_12_first_click_time = current_time

                pin_12_click_count += 1

            else:  # Button released
                # Check if it was a quick press (not held)
                if current_time - pin_12_press_start_time < 0.5:
                    # This was a click, not a hold
                    pass  # Click count already incremented on press
                else:
                    # This was a hold, reset click count
                    pin_12_click_count = 0

        # Check for held button (continuous spinning)
        if current_pin_12_state and not pin_12_is_held:
            if current_time - pin_12_press_start_time > 0.5:  # Held for more than 0.5 seconds
                pin_12_is_held = True
                pin_12_click_count = 0  # Reset click count for hold behavior
                print("Pin 12: Held - Continuous spinning")

        # Handle continuous spinning while held
        if pin_12_is_held and current_pin_12_state:
            # Continuous spinning
            angle = int((current_time * 180) % 360)  # Spin at ~1 revolution per 2 seconds
            my_servo.angle = angle % 180 if angle < 180 else 180 - (angle % 180)

        # Check for click pattern timeouts and execute actions
        if pin_12_click_count > 0 and not current_pin_12_state:
            # Check for triple click (within 1.2 seconds)
            if pin_12_click_count >= 3 and (current_time - pin_12_first_click_time) <= 1.2:
                handle_pin_12_clicks()
            # Check for double click (within 1.0 second)
            elif pin_12_click_count >= 2 and (current_time - pin_12_first_click_time) <= 1.0:
                handle_pin_12_clicks()
            # Check for single click timeout (1.2 seconds after first click)
            elif pin_12_click_count == 1 and (current_time - pin_12_first_click_time) > 1.2:
                handle_pin_12_clicks()

        pin_12_last_state = current_pin_12_state

        # --- Pin 11 Logic (unchanged) ---
        if pin_11_in.value:
            print("Pin 11 triggered: Quarter turn (45 degrees).")
            my_servo.angle = 45
            time.sleep(1) # Hold the position for a second
            my_servo.angle = 0 # Return to start
            time.sleep(1)

        elif not pin_13_in.value and not pin_12_in.value and not pin_11_in.value:
            # Optional: Reset servo to 0 when no signal is active
            # But only if pin 12 is not being held for continuous spinning
            if not pin_12_is_held:
                my_servo.angle = 0

        # The main loop delay. The LED blink check happens more frequently
        # than this, but this prevents the CPU from running at 100% when idle.
        time.sleep(0.01)

if __name__ == "__main__":
    main()
