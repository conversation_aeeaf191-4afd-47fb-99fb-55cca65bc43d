
# Feather RP2040 Servo Control Project

This project contains Python scripts for the Adafruit Feather RP2040.

## Scripts

### `check_connection.py`

This script runs on your computer and checks if the Feather RP2040 is connected via USB. It uses the `lsusb` command, so it is intended for Linux-based systems.

**Usage:**

```bash
python3 check_connection.py
```

### `code.py`

This is a CircuitPython script that runs on the Feather RP2040. It controls a servo motor connected to pin `D5` based on input signals from pins `D11`, `D12`, and `D13`.

**Features:**

- **Pin D13:** When a high signal is received, the servo motor will continuously sweep from 0 to 180 degrees and back.
- **Pin D12:** When a high signal is received, the servo will move to 90 degrees (a half turn) and then return to 0.
- **Pin D11:** When a high signal is received, the servo will move to 45 degrees (a quarter turn) and then return to 0.

**Setup:**

1.  **Install CircuitPython:** Make sure your Feather RP2040 has the latest version of CircuitPython installed.
2.  **Copy `code.py`:** Copy this `code.py` file to the `CIRCUITPY` drive that appears when you connect your Feather to the computer.
3.  **Install Libraries:** You will need the `adafruit_motor` library. Download the Adafruit CircuitPython Library Bundle from the [CircuitPython website](https://circuitpython.org/libraries) and copy the `adafruit_motor` directory into the `lib` folder on your `CIRCUITPY` drive.
4.  **Wiring:**
    -   Connect the servo's signal wire to pin `D5` on the Feather.
    -   Connect the servo's power and ground wires to a suitable power source (e.g., the `3.3V` and `GND` pins on the Feather for a small servo).
    -   To trigger the inputs, connect a wire from the `3.3V` pin on the Feather to `D11`, `D12`, or `D13`.

## Requirements

The `check_connection.py` script has no external Python dependencies. The `code.py` script requires the `adafruit_motor` CircuitPython library.
