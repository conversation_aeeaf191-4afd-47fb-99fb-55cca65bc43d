{"python.languageServer": "<PERSON><PERSON><PERSON>", "python.analysis.diagnosticSeverityOverrides": {"reportMissingModuleSource": "none", "reportShadowedImports": "none"}, "python.analysis.extraPaths": ["/home/<USER>/.config/Code/User/globalStorage/padgettholdings.circuitpythonsync/stubs/circuitpython_stubs-9.2.8/board_definitions/adafruit_feather_rp2040", "/home/<USER>/.vscode/extensions/wmerkens.vscode-circuitpython-v2-0.3.3/boards/0x239A/0x80F2", "/home/<USER>/.vscode/extensions/wmerkens.vscode-circuitpython-v2-0.3.3/stubs", "/home/<USER>/.config/Code/User/globalStorage/wmerkens.vscode-circuitpython-v2/bundle/20250810/adafruit-circuitpython-bundle-py-20250810/lib", "/home/<USER>/public_html/electronics/feather-rp2040/libArchive/libstubs", "/home/<USER>/.config/Code/User/globalStorage/padgettholdings.circuitpythonsync/stubs/circuitpython_stubs-9.2.8"], "circuitpython.board.version": null, "circuitpython.board.vid": "0x239A", "circuitpython.board.pid": "0x80F2", "circuitpythonsync.drivepath": "/media/mrb/CIRCUITPY", "circuitpythonsync.cpfullversion": "9.2.8", "circuitpythonsync.cpboardname": "adafruit_feather_rp2040", "circuitpythonsync.cpbaseversion": "9", "circuitpythonsync.curlibtag": "20250810", "circuitpythonsync.doNotShowWelcome": true}